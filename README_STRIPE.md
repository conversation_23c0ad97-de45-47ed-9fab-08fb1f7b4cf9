# Stripe Payment Gateway Integration

This application now includes a complete Stripe payment gateway integration for processing deposits and subscription payments.

## 🚀 Features

- ✅ **One-time Payments**: Secure deposit processing via Stripe
- ✅ **Subscription Payments**: Recurring billing for subscription plans
- ✅ **Webhook Handling**: Real-time payment status updates
- ✅ **Admin Configuration**: Easy setup through admin panel
- ✅ **Error Handling**: Comprehensive error management with user-friendly messages
- ✅ **Security**: Webhook signature verification and secure API key management
- ✅ **Testing Support**: Full test mode integration with Stripe test cards

## 📋 Quick Start

### 1. Environment Setup

Add these variables to your `.env` file:

```bash
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

### 2. Admin Configuration

1. Go to **Admin Panel** → **Payment Gateways**
2. Click **Add Gateway**
3. Select **Stripe**
4. Enter your Stripe API keys
5. Set mode to **Test** for development
6. Enable the gateway

### 3. Webhook Setup

Create a webhook endpoint in your Stripe dashboard:
- **URL**: `https://yourdomain.com/api/webhooks/stripe`
- **Events**: `payment_intent.succeeded`, `payment_intent.payment_failed`, `invoice.payment_succeeded`, `invoice.payment_failed`

## 🛠 Technical Implementation

### Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client Side   │    │   Server Side    │    │     Stripe      │
│                 │    │                  │    │                 │
│ StripeElements  │───▶│ PaymentService   │───▶│ Payment Intent  │
│ PaymentForm     │    │ StripeService    │    │ Subscriptions   │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        │
                       ┌──────────────────┐              │
                       │   Database       │              │
                       │                  │              │
                       │ Transactions     │              │
                       │ Subscriptions    │              │
                       │ Wallet Balances  │              │
                       └──────────────────┘              │
                                ▲                        │
                                │                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ Webhook Handler  │◀───│ Stripe Webhooks │
                       └──────────────────┘    └─────────────────┘
```

### Key Components

#### 1. Stripe Configuration (`src/lib/stripe/config.ts`)
- Stripe instance initialization
- Configuration validation
- Webhook signature verification
- Error handling utilities

#### 2. Stripe Service (`src/lib/stripe/stripeService.ts`)
- Payment intent creation
- Subscription management
- Customer management
- Price and product creation

#### 3. Payment Service Integration (`src/lib/payment/paymentService.ts`)
- Unified payment processing
- Gateway routing
- Transaction management

#### 4. Webhook Handler (`src/app/api/webhooks/stripe/route.ts`)
- Secure webhook processing
- Event handling
- Database updates

#### 5. Client Components (`src/components/stripe/`)
- `StripeProvider`: Stripe Elements provider
- `StripePaymentForm`: Payment form with card input
- `StripeCheckoutPage`: Complete checkout experience

### Payment Flow

#### Deposit Payments
1. User initiates deposit
2. Payment intent created via Stripe API
3. User redirected to Stripe checkout
4. Payment processed by Stripe
5. Webhook confirms payment
6. Wallet balance updated

#### Subscription Payments
1. User selects subscription plan
2. Stripe subscription created
3. Payment intent for first payment
4. User completes payment
5. Subscription activated
6. Recurring payments handled automatically

## 🧪 Testing

### Test Cards

Use these Stripe test card numbers:

```bash
# Successful payments
****************  # Visa
****************  # Mastercard

# Declined payments
****************  # Generic decline
****************  # Insufficient funds

# 3D Secure
****************  # Requires authentication
```

### Test Scenarios

1. **Successful Deposit**:
   - Use test card `****************`
   - Verify wallet balance increases
   - Check transaction history

2. **Failed Payment**:
   - Use test card `****************`
   - Verify error handling
   - Check transaction marked as failed

3. **Webhook Processing**:
   - Monitor webhook delivery in Stripe dashboard
   - Verify database updates
   - Check error logs

## 🔧 Configuration Options

### Gateway Settings

| Setting | Description | Default |
|---------|-------------|---------|
| Deposit Fee (%) | Percentage fee for deposits | 2.90% |
| Fixed Fee ($) | Fixed fee per transaction | $0.30 |
| Min Deposit | Minimum deposit amount | $1.00 |
| Max Deposit | Maximum deposit amount | $10,000.00 |
| Currency | Supported currency | USD |

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `STRIPE_PUBLISHABLE_KEY` | Yes | Server-side publishable key |
| `STRIPE_SECRET_KEY` | Yes | Server-side secret key |
| `STRIPE_WEBHOOK_SECRET` | Yes | Webhook signature verification |
| `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` | Yes | Client-side publishable key |

## 🚨 Security Considerations

1. **API Key Security**:
   - Never expose secret keys in client code
   - Use environment variables for all keys
   - Rotate keys regularly

2. **Webhook Security**:
   - Always verify webhook signatures
   - Use HTTPS for webhook endpoints
   - Implement idempotency for webhook processing

3. **Payment Security**:
   - PCI compliance handled by Stripe
   - No card data stored locally
   - Secure tokenization for recurring payments

## 📊 Monitoring & Logging

### Error Handling

The integration includes comprehensive error handling:

- **User-friendly messages** for common payment errors
- **Automatic retry logic** for transient failures
- **Detailed logging** for debugging
- **Toast notifications** for real-time feedback

### Monitoring Points

1. **Payment Success Rate**: Track successful vs failed payments
2. **Webhook Delivery**: Monitor webhook processing
3. **Error Rates**: Track and alert on error spikes
4. **Transaction Volume**: Monitor payment volumes

## 🔄 Production Deployment

### Pre-deployment Checklist

- [ ] Stripe account verified and activated
- [ ] Live API keys configured
- [ ] Webhook endpoint accessible via HTTPS
- [ ] SSL certificate valid
- [ ] Test payments completed successfully
- [ ] Error handling tested
- [ ] Monitoring configured

### Go-Live Steps

1. **Switch to live keys** in environment variables
2. **Update admin configuration** to live mode
3. **Test with small real payment**
4. **Monitor webhook delivery**
5. **Verify transaction processing**

## 📚 Documentation

- **Full Setup Guide**: See `docs/STRIPE_INTEGRATION.md`
- **API Documentation**: Inline code comments
- **Stripe Docs**: https://stripe.com/docs

## 🆘 Troubleshooting

### Common Issues

1. **Webhook signature verification failed**
   - Check webhook secret is correct
   - Ensure endpoint is HTTPS
   - Verify raw body is passed to verification

2. **Payment intent creation failed**
   - Verify API keys are active
   - Check amount is within limits
   - Ensure currency is supported

3. **Payments not completing**
   - Check webhook delivery
   - Verify event processing
   - Review application logs

### Debug Mode

Enable debug logging:
```bash
STRIPE_DEBUG=true
```

## 🤝 Support

For issues with the Stripe integration:

1. Check the troubleshooting section
2. Review application logs
3. Test in Stripe's test mode
4. Contact development team with error details

---

**Note**: This integration follows Stripe's best practices and security guidelines. Always test thoroughly before deploying to production.
