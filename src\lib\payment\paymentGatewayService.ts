import { db } from "@/lib/db";
import { paymentGateways } from "@/lib/db/schema";
import { eq, and, desc } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { validateStripeConfig } from "@/lib/stripe/config";

export interface PaymentGateway {
  id: string;
  name: string;
  displayName: string;
  type: 'stripe' | 'paypal' | 'sslcommerz' | 'bkash' | 'nagad' | 'rocket' | 'bank' | 'uddoktapay' | 'manual' | 'wallet';
  isActive: boolean;
  config: any;
  depositFee: string;
  depositFixedFee: string;
  minDeposit: string;
  maxDeposit: string;
  currency: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentGatewayFormData {
  name: string;
  displayName: string;
  type: PaymentGateway['type'];
  isActive: boolean;
  config: any;
  depositFee: string;
  depositFixedFee: string;
  minDeposit: string;
  maxDeposit: string;
  currency: string;
  sortOrder: number;
}

export interface StripeConfig {
  publishableKey: string;
  secretKey: string;
  webhookSecret: string;
  mode?: 'test' | 'live';
}

export interface PayPalConfig {
  clientId: string;
  clientSecret: string;
  mode: 'sandbox' | 'live';
}

export interface UddoktaPayConfig {
  apiKey: string;
  apiUrl: string;
  storeId: string;
}

export interface ManualConfig {
  instructions: string;
  accountDetails: string;
}

export class PaymentGatewayService {
  /**
   * Get all payment gateways
   */
  static async getAllPaymentGateways(): Promise<PaymentGateway[]> {
    const gateways = await db.query.paymentGateways.findMany({
      orderBy: [desc(paymentGateways.sortOrder), desc(paymentGateways.createdAt)],
    });

    return gateways.map(gateway => ({
      ...gateway,
      createdAt: gateway.createdAt.toISOString(),
      updatedAt: gateway.updatedAt.toISOString(),
    }));
  }

  /**
   * Get active payment gateways only
   */
  static async getActivePaymentGateways(): Promise<PaymentGateway[]> {
    const gateways = await db.query.paymentGateways.findMany({
      where: eq(paymentGateways.isActive, true),
      orderBy: [desc(paymentGateways.sortOrder), desc(paymentGateways.createdAt)],
    });

    return gateways.map(gateway => ({
      ...gateway,
      createdAt: gateway.createdAt.toISOString(),
      updatedAt: gateway.updatedAt.toISOString(),
    }));
  }

  /**
   * Get payment gateway by ID
   */
  static async getPaymentGatewayById(id: string): Promise<PaymentGateway | null> {
    const gateway = await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.id, id),
    });

    if (!gateway) return null;

    return {
      ...gateway,
      createdAt: gateway.createdAt.toISOString(),
      updatedAt: gateway.updatedAt.toISOString(),
    };
  }

  /**
   * Get payment gateway by name
   */
  static async getPaymentGatewayByName(name: string): Promise<PaymentGateway | null> {
    const gateway = await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.name, name),
    });

    if (!gateway) return null;

    return {
      ...gateway,
      createdAt: gateway.createdAt.toISOString(),
      updatedAt: gateway.updatedAt.toISOString(),
    };
  }

  /**
   * Get payment gateways by type
   */
  static async getPaymentGatewaysByType(type: PaymentGateway['type']): Promise<PaymentGateway[]> {
    const gateways = await db.query.paymentGateways.findMany({
      where: eq(paymentGateways.type, type),
      orderBy: [desc(paymentGateways.sortOrder), desc(paymentGateways.createdAt)],
    });

    return gateways.map(gateway => ({
      ...gateway,
      createdAt: gateway.createdAt.toISOString(),
      updatedAt: gateway.updatedAt.toISOString(),
    }));
  }

  /**
   * Create a new payment gateway
   */
  static async createPaymentGateway(data: PaymentGatewayFormData): Promise<string> {
    const gatewayId = uuidv4();

    // Check if gateway with same name already exists
    const existingGateway = await this.getPaymentGatewayByName(data.name);
    if (existingGateway) {
      throw new Error('Payment gateway with this name already exists');
    }

    await db.insert(paymentGateways).values({
      id: gatewayId,
      ...data,
    });

    return gatewayId;
  }

  /**
   * Update an existing payment gateway
   */
  static async updatePaymentGateway(id: string, data: Partial<PaymentGatewayFormData>): Promise<void> {
    const existingGateway = await this.getPaymentGatewayById(id);
    if (!existingGateway) {
      throw new Error('Payment gateway not found');
    }

    // If name is being changed, check for conflicts
    if (data.name && data.name !== existingGateway.name) {
      const conflictingGateway = await this.getPaymentGatewayByName(data.name);
      if (conflictingGateway && conflictingGateway.id !== id) {
        throw new Error('Payment gateway with this name already exists');
      }
    }

    await db.update(paymentGateways)
      .set(data)
      .where(eq(paymentGateways.id, id));
  }

  /**
   * Delete a payment gateway
   */
  static async deletePaymentGateway(id: string): Promise<void> {
    const existingGateway = await this.getPaymentGatewayById(id);
    if (!existingGateway) {
      throw new Error('Payment gateway not found');
    }

    await db.delete(paymentGateways)
      .where(eq(paymentGateways.id, id));
  }

  /**
   * Toggle payment gateway active status
   */
  static async togglePaymentGatewayStatus(id: string): Promise<void> {
    const gateway = await this.getPaymentGatewayById(id);
    if (!gateway) {
      throw new Error('Payment gateway not found');
    }

    await db.update(paymentGateways)
      .set({ isActive: !gateway.isActive })
      .where(eq(paymentGateways.id, id));
  }

  /**
   * Validate gateway configuration based on type
   */
  static validateGatewayConfig(type: PaymentGateway['type'], config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    switch (type) {
      case 'stripe':
        const stripeConfig = config as StripeConfig;
        const stripeValidation = validateStripeConfig(stripeConfig);
        if (!stripeValidation.isValid) {
          errors.push(...stripeValidation.errors);
        }
        break;

      case 'uddoktapay':
        const uddoktaConfig = config as UddoktaPayConfig;
        if (!uddoktaConfig.apiKey) errors.push('API key is required');
        if (!uddoktaConfig.apiUrl) errors.push('API URL is required');
        if (!uddoktaConfig.storeId) errors.push('Store ID is required');
        break;

      case 'manual':
        const manualConfig = config as ManualConfig;
        if (!manualConfig.instructions) errors.push('Payment instructions are required');
        break;

      case 'wallet':
        // Wallet gateway doesn't require additional config
        break;

      default:
        errors.push('Unsupported gateway type');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get supported gateway types with their display information
   */
  static getSupportedGatewayTypes() {
    return [
      { value: 'stripe', label: 'Stripe', description: 'Credit/debit card payments via Stripe' },
      { value: 'manual', label: 'Manual Payment Method', description: 'Manual payment processing with instructions' },
      { value: 'uddoktapay', label: 'UddoktaPay', description: 'UddoktaPay payment gateway' },
      { value: 'wallet', label: 'Wallet Balance', description: 'Internal wallet balance payments' },
    ];
  }
}
