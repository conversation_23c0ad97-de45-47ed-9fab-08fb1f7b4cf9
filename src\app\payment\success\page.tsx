"use client";

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { CheckCircleIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [paymentDetails, setPaymentDetails] = useState<any>(null);

  const paymentIntentId = searchParams.get('payment_intent');
  const type = searchParams.get('type') || 'deposit';
  const subscriptionId = searchParams.get('subscription_id');

  useEffect(() => {
    if (!paymentIntentId) {
      toast.error('Invalid payment session');
      router.push('/wallet');
      return;
    }

    // Fetch payment details
    fetchPaymentDetails();
  }, [paymentIntentId, router]);

  const fetchPaymentDetails = async () => {
    try {
      // In a real implementation, you might want to fetch payment details from your API
      // For now, we'll just show a success message
      setPaymentDetails({
        paymentIntentId,
        type,
        subscriptionId,
        status: 'succeeded',
      });
      
      toast.success('Payment completed successfully!');
    } catch (error) {
      console.error('Error fetching payment details:', error);
      toast.error('Error verifying payment. Please contact support if funds were charged.');
    } finally {
      setLoading(false);
    }
  };

  const handleContinue = () => {
    if (type === 'subscription_payment') {
      router.push('/subscription');
    } else {
      router.push('/wallet');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying payment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
          {/* Success Icon */}
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>

          {/* Success Message */}
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Payment Successful!
          </h1>
          
          <p className="text-gray-600 mb-6">
            {type === 'subscription_payment' 
              ? 'Your subscription has been activated successfully.'
              : 'Your deposit has been processed and added to your wallet.'
            }
          </p>

          {/* Payment Details */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Payment Details</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Payment ID:</span>
                <span className="font-mono text-xs">{paymentIntentId}</span>
              </div>
              <div className="flex justify-between">
                <span>Type:</span>
                <span className="capitalize">
                  {type === 'subscription_payment' ? 'Subscription' : 'Deposit'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Status:</span>
                <span className="text-green-600 font-medium">Completed</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={handleContinue}
              className="w-full flex items-center justify-center"
              size="lg"
            >
              <span>
                {type === 'subscription_payment' 
                  ? 'View Subscription' 
                  : 'View Wallet'
                }
              </span>
              <ArrowRightIcon className="h-4 w-4 ml-2" />
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/')}
              className="w-full"
            >
              Return to Dashboard
            </Button>
          </div>

          {/* Help Text */}
          <div className="mt-6 text-xs text-gray-500">
            <p>
              You will receive a confirmation email shortly. If you have any questions,
              please contact our{' '}
              <a href="/support" className="text-blue-600 hover:text-blue-700">
                support team
              </a>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <PaymentSuccessContent />
    </Suspense>
  );
}
