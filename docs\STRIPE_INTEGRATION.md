# Stripe Payment Gateway Integration

This document provides comprehensive instructions for setting up and configuring the Stripe payment gateway integration in your application.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Stripe Account Setup](#stripe-account-setup)
3. [Environment Configuration](#environment-configuration)
4. [Admin Panel Configuration](#admin-panel-configuration)
5. [Webhook Configuration](#webhook-configuration)
6. [Testing](#testing)
7. [Production Deployment](#production-deployment)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

- Active Stripe account (https://stripe.com)
- Application deployed and accessible via HTTPS (required for webhooks)
- Admin access to your application

## Stripe Account Setup

### 1. Create a Stripe Account

1. Visit https://stripe.com and create an account
2. Complete the account verification process
3. Navigate to the Stripe Dashboard

### 2. Get API Keys

1. In the Stripe Dashboard, go to **Developers** > **API keys**
2. Copy the following keys:
   - **Publishable key** (starts with `pk_test_` for test mode or `pk_live_` for live mode)
   - **Secret key** (starts with `sk_test_` for test mode or `sk_live_` for live mode)

### 3. Create Webhook Endpoint

1. Go to **Developers** > **Webhooks**
2. Click **Add endpoint**
3. Set the endpoint URL to: `https://yourdomain.com/api/webhooks/stripe`
4. Select the following events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
5. Click **Add endpoint**
6. Copy the **Signing secret** (starts with `whsec_`)

## Environment Configuration

### 1. Update Environment Variables

Add the following variables to your `.env` file:

```bash
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

### 2. Environment Variable Descriptions

- `STRIPE_PUBLISHABLE_KEY`: Server-side publishable key for Stripe API calls
- `STRIPE_SECRET_KEY`: Server-side secret key for Stripe API calls
- `STRIPE_WEBHOOK_SECRET`: Secret for verifying webhook signatures
- `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`: Client-side publishable key for Stripe Elements

**Important**: 
- Use test keys (`pk_test_` and `sk_test_`) for development
- Use live keys (`pk_live_` and `sk_live_`) for production
- Never expose secret keys in client-side code

## Admin Panel Configuration

### 1. Access Payment Gateways

1. Log in to your application as an administrator
2. Navigate to **Admin Panel** > **Payment Gateways**
3. Click **Add Gateway**

### 2. Configure Stripe Gateway

1. Select **Stripe** as the gateway type
2. Fill in the configuration fields:

   **Basic Information:**
   - **Gateway Name**: `stripe_main` (or your preferred identifier)
   - **Display Name**: `Credit/Debit Card` (what users will see)

   **Stripe Configuration:**
   - **Publishable Key**: Your Stripe publishable key
   - **Secret Key**: Your Stripe secret key
   - **Webhook Secret**: Your Stripe webhook secret
   - **Mode**: Select `Test` for development or `Live` for production

   **Fee Configuration:**
   - **Deposit Fee (%)**: `2.90` (Stripe's standard rate)
   - **Fixed Fee ($)**: `0.30` (Stripe's standard fixed fee)

   **Transaction Limits:**
   - **Min Deposit ($)**: `1.00`
   - **Max Deposit ($)**: `10000.00` (adjust as needed)
   - **Currency**: `USD` (or your preferred currency)

3. **Enable the gateway** by checking the "Enable this gateway" checkbox
4. Click **Create Gateway**

### 3. Verify Configuration

1. The gateway should appear in the payment gateways list
2. Status should show as "Active"
3. Test the configuration by making a small test payment

## Webhook Configuration

### 1. Webhook Endpoint

The webhook endpoint is automatically available at:
```
https://yourdomain.com/api/webhooks/stripe
```

### 2. Webhook Events Handled

The integration handles the following Stripe events:

- **payment_intent.succeeded**: One-time payment completed
- **payment_intent.payment_failed**: One-time payment failed
- **invoice.payment_succeeded**: Subscription payment completed
- **invoice.payment_failed**: Subscription payment failed
- **customer.subscription.created**: New subscription created
- **customer.subscription.updated**: Subscription status changed
- **customer.subscription.deleted**: Subscription cancelled

### 3. Webhook Security

- All webhooks are verified using Stripe's signature verification
- Invalid signatures are rejected with a 400 status code
- Webhook processing is idempotent to handle duplicate events

## Testing

### 1. Test Mode Setup

1. Use test API keys in your environment variables
2. Configure the Stripe gateway in test mode
3. Use Stripe's test card numbers for testing

### 2. Test Card Numbers

Use these test card numbers for different scenarios:

**Successful Payments:**
- `****************` (Visa)
- `****************` (Visa Debit)
- `****************` (Mastercard)

**Declined Payments:**
- `****************` (Generic decline)
- `****************` (Insufficient funds)
- `****************` (Lost card)

**3D Secure Authentication:**
- `****************` (Requires authentication)

### 3. Testing Checklist

- [ ] Deposit payments work correctly
- [ ] Failed payments are handled properly
- [ ] Webhooks are received and processed
- [ ] Transaction statuses are updated correctly
- [ ] User balances are updated after successful payments
- [ ] Error messages are user-friendly

## Production Deployment

### 1. Switch to Live Mode

1. Update environment variables with live API keys:
   ```bash
   STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
   STRIPE_SECRET_KEY=sk_live_your_live_secret_key
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
   ```

2. Update the Stripe gateway configuration in admin panel:
   - Change **Mode** to `Live`
   - Update API keys to live keys
   - Update webhook secret to live webhook secret

### 2. SSL Certificate

Ensure your domain has a valid SSL certificate. Stripe requires HTTPS for:
- Webhook endpoints
- Payment processing
- API calls

### 3. Domain Verification

1. In Stripe Dashboard, go to **Settings** > **Account details**
2. Add your domain to the approved domains list
3. Verify domain ownership if required

## Troubleshooting

### Common Issues

**1. Webhook Signature Verification Failed**
- Check that the webhook secret is correct
- Ensure the webhook URL is accessible via HTTPS
- Verify the webhook endpoint is receiving the raw request body

**2. Payment Intent Creation Failed**
- Verify API keys are correct and active
- Check that the amount is within Stripe's limits
- Ensure currency is supported by Stripe

**3. Gateway Configuration Invalid**
- Verify all required fields are filled
- Check that test/live keys match the selected mode
- Ensure webhook secret format is correct (starts with `whsec_`)

**4. Payments Not Completing**
- Check webhook endpoint is receiving events
- Verify webhook events are being processed correctly
- Check application logs for errors

### Debug Mode

To enable debug logging for Stripe operations:

1. Set environment variable: `STRIPE_DEBUG=true`
2. Check application logs for detailed Stripe API calls
3. Monitor webhook delivery in Stripe Dashboard

### Support

For additional support:

1. Check Stripe's documentation: https://stripe.com/docs
2. Review application logs for error details
3. Contact your development team with specific error messages
4. Use Stripe's test mode to isolate issues

## Security Best Practices

1. **Never expose secret keys** in client-side code
2. **Always verify webhook signatures** before processing
3. **Use HTTPS** for all Stripe-related endpoints
4. **Regularly rotate API keys** for security
5. **Monitor webhook delivery** for failed attempts
6. **Implement proper error handling** for all payment flows
7. **Log security events** for audit purposes

## Quick Setup Checklist

For administrators who want a quick setup guide:

### Development Setup (5 minutes)

1. **Get Stripe test keys**:
   - Sign up at https://stripe.com
   - Copy test publishable key (`pk_test_...`)
   - Copy test secret key (`sk_test_...`)

2. **Update environment**:
   ```bash
   STRIPE_PUBLISHABLE_KEY=pk_test_your_key
   STRIPE_SECRET_KEY=sk_test_your_key
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key
   ```

3. **Configure in admin panel**:
   - Go to Admin > Payment Gateways
   - Add Stripe gateway with test keys
   - Set mode to "Test"
   - Enable the gateway

4. **Test payment**:
   - Use card number: `****************`
   - Any future expiry date and CVC

### Production Setup (10 minutes)

1. **Complete Stripe account verification**
2. **Set up webhook** at `https://yourdomain.com/api/webhooks/stripe`
3. **Get live keys** and webhook secret
4. **Update environment** with live keys
5. **Update admin configuration** to live mode
6. **Test with real card** (small amount)

## Additional Resources

- [Stripe Documentation](https://stripe.com/docs)
- [Stripe API Reference](https://stripe.com/docs/api)
- [Stripe Webhooks Guide](https://stripe.com/docs/webhooks)
- [Stripe Testing Guide](https://stripe.com/docs/testing)
