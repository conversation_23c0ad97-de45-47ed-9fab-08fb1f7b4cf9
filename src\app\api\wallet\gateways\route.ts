import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { WalletService } from "@/lib/wallet/walletService";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const excludeWallet = searchParams.get('excludeWallet') === 'true';

    const gateways = await WalletService.getActivePaymentGateways();

    // Filter out wallet gateway for deposits if requested
    const filteredGateways = excludeWallet
      ? gateways.filter(gateway => gateway.type !== 'wallet')
      : gateways;

    // Remove sensitive config data before sending to client
    const sanitizedGateways = filteredGateways.map(gateway => ({
      id: gateway.id,
      name: gateway.name,
      displayName: gateway.displayName,
      type: gateway.type,
      depositFee: gateway.depositFee,
      depositFixedFee: gateway.depositFixedFee,
      minDeposit: gateway.minDeposit,
      maxDeposit: gateway.maxDeposit,
      currency: gateway.currency,
      isActive: gateway.isActive,
    }));

    return NextResponse.json({
      success: true,
      data: sanitizedGateways,
    });
  } catch (error: any) {
    console.error("Error fetching payment gateways:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch payment gateways" 
      },
      { status: 500 }
    );
  }
}
