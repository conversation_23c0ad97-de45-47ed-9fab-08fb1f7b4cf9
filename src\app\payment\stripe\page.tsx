"use client";

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { StripeProvider } from '@/components/stripe/StripeProvider';
import { StripePaymentForm } from '@/components/stripe/StripePaymentForm';
import { Button } from '@/components/ui/Button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

function StripeCheckoutContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [paymentData, setPaymentData] = useState<any>(null);

  const clientSecret = searchParams.get('client_secret');
  const paymentIntentId = searchParams.get('payment_intent');
  const amount = searchParams.get('amount');
  const currency = searchParams.get('currency') || 'USD';

  useEffect(() => {
    if (!clientSecret || !paymentIntentId) {
      toast.error('Invalid payment session. Please try again.');
      router.push('/wallet');
      return;
    }

    setPaymentData({
      clientSecret,
      paymentIntentId,
      amount: amount ? parseFloat(amount) : 0,
      currency,
    });
    setLoading(false);
  }, [clientSecret, paymentIntentId, amount, currency, router]);

  const handlePaymentSuccess = (paymentIntent: any) => {
    console.log('Payment successful:', paymentIntent);
    
    // Redirect to success page with payment details
    router.push(`/payment/success?payment_intent=${paymentIntent.id}&type=deposit`);
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
    toast.error(error);
  };

  const handleGoBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading payment form...</p>
        </div>
      </div>
    );
  }

  if (!paymentData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">Invalid payment session</p>
          <Button onClick={() => router.push('/wallet')} className="mt-4">
            Return to Wallet
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex items-center mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleGoBack}
              className="mr-3"
            >
              <ArrowLeftIcon className="h-4 w-4" />
            </Button>
            <h1 className="text-xl font-semibold text-gray-900">
              Complete Payment
            </h1>
          </div>
          
          <div className="text-center">
            <p className="text-gray-600">Secure payment powered by Stripe</p>
          </div>
        </div>

        {/* Payment Form */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <StripeProvider clientSecret={paymentData.clientSecret}>
            <StripePaymentForm
              amount={paymentData.amount}
              currency={paymentData.currency}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              returnUrl={`${window.location.origin}/payment/success`}
              requireBillingAddress={false}
              submitButtonText={`Pay ${new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: paymentData.currency.toUpperCase(),
              }).format(paymentData.amount)}`}
            />
          </StripeProvider>
        </div>

        {/* Help Text */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>
            Having trouble? Contact our{' '}
            <a href="/support" className="text-blue-600 hover:text-blue-700">
              support team
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function StripeCheckoutPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <StripeCheckoutContent />
    </Suspense>
  );
}
