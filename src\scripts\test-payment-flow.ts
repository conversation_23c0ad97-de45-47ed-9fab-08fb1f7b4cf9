import { PaymentService } from "@/lib/payment/paymentService";
import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";

async function testPaymentFlow() {
  console.log('🧪 Testing Payment Gateway Flow\n');

  try {
    // Test 1: Get available payment gateways
    console.log('1️⃣ Testing PaymentService.getAvailablePaymentGateways()');
    const availableGateways = await PaymentService.getAvailablePaymentGateways();
    console.log(`   ✅ Found ${availableGateways.length} available gateways`);
    
    availableGateways.forEach(gateway => {
      console.log(`   - ${gateway.displayName} (${gateway.type}) - Active: ${gateway.isActive}`);
    });
    console.log('');

    // Test 2: Get available payment gateways excluding wallet
    console.log('2️⃣ Testing PaymentService.getAvailablePaymentGateways({ excludeWallet: true })');
    const gatewaysWithoutWallet = await PaymentService.getAvailablePaymentGateways({ excludeWallet: true });
    console.log(`   ✅ Found ${gatewaysWithoutWallet.length} gateways (excluding wallet)`);
    
    gatewaysWithoutWallet.forEach(gateway => {
      console.log(`   - ${gateway.displayName} (${gateway.type})`);
    });
    console.log('');

    // Test 3: Validate gateway configurations
    console.log('3️⃣ Testing gateway configurations');
    const allGateways = await PaymentGatewayService.getAllPaymentGateways();
    
    for (const gateway of allGateways) {
      const validation = PaymentGatewayService.validateGatewayConfig(
        gateway.type as any,
        gateway.config
      );
      
      console.log(`   ${validation.isValid ? '✅' : '❌'} ${gateway.displayName} (${gateway.type})`);
      if (!validation.isValid) {
        validation.errors.forEach(error => {
          console.log(`      - ${error}`);
        });
      }
    }
    console.log('');

    // Test 4: Test payment processing for each active gateway
    console.log('4️⃣ Testing payment processing (dry run)');
    const activeGateways = availableGateways.filter(g => g.isActive !== false);
    
    for (const gateway of activeGateways) {
      console.log(`   Testing ${gateway.displayName}...`);
      
      try {
        // This is a dry run - we won't actually process payments
        const testRequest = {
          amount: '10.00',
          currency: 'USD',
          gatewayId: gateway.id,
          userId: 'test-user-id',
          metadata: {
            test: true,
            type: 'test_payment'
          }
        };

        // Get the full gateway details for validation
        const fullGateway = await PaymentGatewayService.getPaymentGatewayById(gateway.id);
        
        if (!fullGateway) {
          console.log(`      ❌ Gateway not found`);
          continue;
        }

        if (!fullGateway.isActive) {
          console.log(`      ❌ Gateway not active`);
          continue;
        }

        const configValidation = PaymentGatewayService.validateGatewayConfig(
          fullGateway.type as any,
          fullGateway.config
        );

        if (!configValidation.isValid) {
          console.log(`      ❌ Configuration invalid: ${configValidation.errors.join(', ')}`);
          continue;
        }

        console.log(`      ✅ Gateway ready for payment processing`);
        
      } catch (error: any) {
        console.log(`      ❌ Error: ${error.message}`);
      }
    }
    console.log('');

    console.log('🎉 Payment gateway flow test completed!');

  } catch (error: any) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testPaymentFlow().catch(console.error);
}

export { testPaymentFlow };
