import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { PaymentService } from "@/lib/payment/paymentService";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const gateways = await PaymentService.getAvailablePaymentGateways();

    return NextResponse.json({
      success: true,
      data: gateways,
    });
  } catch (error: any) {
    console.error("Error fetching payment gateways:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch payment gateways"
      },
      { status: 500 }
    );
  }
}
