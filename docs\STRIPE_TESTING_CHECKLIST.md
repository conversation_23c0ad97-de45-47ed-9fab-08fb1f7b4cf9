# Stripe Integration Testing Checklist

This document provides a comprehensive testing checklist to ensure the Stripe payment gateway integration is working correctly.

## 🚀 Quick Validation

Run the automated validation script:

```bash
npm run validate:stripe
```

This will check:
- Environment variables are set
- Active Stripe gateway exists
- Gateway configuration is valid

## 🧪 Automated Testing

Run the full test suite:

```bash
npm run test:stripe
```

This will test:
- Configuration validation
- Payment intent creation
- Payment processing
- Webhook handling
- Error handling

## ✅ Manual Testing Checklist

### 1. Environment Setup

- [ ] **Environment Variables Set**
  - [ ] `STRIPE_PUBLISHABLE_KEY` is set
  - [ ] `STRIPE_SECRET_KEY` is set
  - [ ] `STRIPE_WEBHOOK_SECRET` is set (if using webhooks)
  - [ ] `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` is set

- [ ] **Key Format Validation**
  - [ ] Publishable key starts with `pk_test_` (test) or `pk_live_` (live)
  - [ ] Secret key starts with `sk_test_` (test) or `sk_live_` (live)
  - [ ] Webhook secret starts with `whsec_`

### 2. Admin Panel Configuration

- [ ] **Gateway Creation**
  - [ ] Navigate to Admin Panel → Payment Gateways
  - [ ] Stripe gateway is listed
  - [ ] Gateway status shows as "Active"
  - [ ] Configuration fields are properly filled

- [ ] **Gateway Settings**
  - [ ] Publishable key is correct
  - [ ] Secret key is correct
  - [ ] Webhook secret is correct
  - [ ] Mode matches environment (test/live)
  - [ ] Fee configuration is appropriate
  - [ ] Transaction limits are set

### 3. Deposit Payment Testing

#### Test Successful Payments

- [ ] **Basic Deposit Flow**
  - [ ] Go to wallet/deposit page
  - [ ] Select Stripe as payment method
  - [ ] Enter amount (e.g., $10.00)
  - [ ] Click "Deposit"
  - [ ] Redirected to Stripe checkout
  - [ ] Payment form loads correctly

- [ ] **Test Card: ******************
  - [ ] Enter card number: `****************`
  - [ ] Enter future expiry date (e.g., 12/25)
  - [ ] Enter any 3-digit CVC (e.g., 123)
  - [ ] Click "Pay Now"
  - [ ] Payment succeeds
  - [ ] Redirected to success page
  - [ ] Wallet balance updated correctly
  - [ ] Transaction appears in history

#### Test Failed Payments

- [ ] **Declined Card: ******************
  - [ ] Use card number: `****************`
  - [ ] Payment should be declined
  - [ ] User-friendly error message shown
  - [ ] No wallet balance change
  - [ ] Transaction marked as failed

- [ ] **Insufficient Funds: ******************
  - [ ] Use card number: `****************`
  - [ ] "Insufficient funds" error shown
  - [ ] Proper error handling

#### Test Edge Cases

- [ ] **Minimum Amount**
  - [ ] Try deposit below minimum (should be rejected)
  - [ ] Try exact minimum amount (should work)

- [ ] **Maximum Amount**
  - [ ] Try deposit above maximum (should be rejected)
  - [ ] Try exact maximum amount (should work)

- [ ] **Invalid Amounts**
  - [ ] Try negative amount (should be rejected)
  - [ ] Try zero amount (should be rejected)
  - [ ] Try non-numeric amount (should be rejected)

### 4. Subscription Payment Testing

- [ ] **Subscription Creation**
  - [ ] Select a paid subscription plan
  - [ ] Choose Stripe as payment method
  - [ ] Complete payment flow
  - [ ] Subscription activated
  - [ ] User gains subscription benefits

- [ ] **Recurring Payments** (if applicable)
  - [ ] Subscription renews automatically
  - [ ] Payment processed successfully
  - [ ] Subscription remains active

### 5. Webhook Testing

- [ ] **Webhook Endpoint**
  - [ ] Webhook URL is accessible: `https://yourdomain.com/api/webhooks/stripe`
  - [ ] Returns 200 for valid requests
  - [ ] Returns 400 for invalid signatures

- [ ] **Event Processing**
  - [ ] `payment_intent.succeeded` events processed
  - [ ] `payment_intent.payment_failed` events processed
  - [ ] Database updated correctly
  - [ ] User balances updated

- [ ] **Webhook Delivery** (in Stripe Dashboard)
  - [ ] Events are being delivered
  - [ ] No failed deliveries
  - [ ] Response times are reasonable

### 6. Error Handling Testing

- [ ] **Network Errors**
  - [ ] Disconnect internet during payment
  - [ ] Appropriate error message shown
  - [ ] User can retry payment

- [ ] **Invalid Configuration**
  - [ ] Test with invalid API keys
  - [ ] Proper error handling
  - [ ] No sensitive data exposed

- [ ] **Rate Limiting**
  - [ ] Multiple rapid payment attempts
  - [ ] Rate limit errors handled gracefully

### 7. Security Testing

- [ ] **API Key Security**
  - [ ] Secret keys not exposed in client code
  - [ ] Keys stored securely in environment variables
  - [ ] No keys in browser developer tools

- [ ] **Webhook Security**
  - [ ] Webhook signatures verified
  - [ ] Invalid signatures rejected
  - [ ] Replay attacks prevented

- [ ] **HTTPS Requirements**
  - [ ] All Stripe endpoints use HTTPS
  - [ ] No mixed content warnings
  - [ ] SSL certificate valid

### 8. User Experience Testing

- [ ] **Payment Form**
  - [ ] Form loads quickly
  - [ ] Card input is responsive
  - [ ] Error messages are clear
  - [ ] Success feedback is immediate

- [ ] **Mobile Testing**
  - [ ] Payment form works on mobile
  - [ ] Touch interactions work
  - [ ] Form is properly sized

- [ ] **Browser Compatibility**
  - [ ] Works in Chrome
  - [ ] Works in Firefox
  - [ ] Works in Safari
  - [ ] Works in Edge

### 9. Performance Testing

- [ ] **Load Times**
  - [ ] Payment form loads in < 3 seconds
  - [ ] Stripe Elements load quickly
  - [ ] No blocking JavaScript

- [ ] **Payment Processing**
  - [ ] Payments process in reasonable time
  - [ ] No timeouts during normal operation
  - [ ] Webhook processing is fast

### 10. Production Readiness

- [ ] **Live Mode Testing**
  - [ ] Switch to live API keys
  - [ ] Test with real card (small amount)
  - [ ] Verify live webhook delivery
  - [ ] Monitor for any issues

- [ ] **Monitoring Setup**
  - [ ] Error logging configured
  - [ ] Payment success/failure tracking
  - [ ] Webhook delivery monitoring
  - [ ] Alert system for failures

## 🔧 Test Data

### Test Card Numbers

```bash
# Successful payments
****************  # Visa
****************  # Visa (debit)
****************  # Mastercard
2223003122003222  # Mastercard (2-series)
****************  # Mastercard (debit)
****************  # Visa (prepaid)

# Declined payments
****************  # Generic decline
****************  # Insufficient funds
****************  # Lost card
****************  # Stolen card
****************  # Expired card
****************  # Incorrect CVC
****************  # Processing error

# 3D Secure authentication
****************  # Requires authentication
****************  # Requires authentication (prepaid)
```

### Test Amounts

```bash
# Special amounts for testing
$0.50   # Below minimum (should fail)
$1.00   # Minimum amount
$10.00  # Normal amount
$100.00 # Larger amount
$10000.00 # Maximum amount
$10000.01 # Above maximum (should fail)
```

## 📊 Success Criteria

The Stripe integration is considered successful when:

- [ ] All automated tests pass
- [ ] Manual testing checklist completed
- [ ] No critical errors in logs
- [ ] Payment success rate > 95%
- [ ] Webhook delivery success rate > 99%
- [ ] Average payment processing time < 5 seconds
- [ ] No security vulnerabilities identified
- [ ] User experience is smooth and intuitive

## 🚨 Common Issues & Solutions

### Payment Intent Creation Fails
- Check API keys are correct and active
- Verify amount is within Stripe limits
- Ensure currency is supported

### Webhook Signature Verification Fails
- Verify webhook secret is correct
- Check endpoint is accessible via HTTPS
- Ensure raw request body is used for verification

### Payments Not Completing
- Check webhook delivery in Stripe dashboard
- Verify webhook events are being processed
- Review application logs for errors

### Configuration Errors
- Run `npm run validate:stripe` to check setup
- Verify all environment variables are set
- Check admin panel gateway configuration

---

**Note**: Always test in Stripe's test mode before going live. Use the provided test card numbers to simulate different scenarios.
