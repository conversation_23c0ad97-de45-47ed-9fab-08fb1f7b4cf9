import { WalletService } from "@/lib/wallet/walletService";
import { SubscriptionService } from "@/lib/subscription/subscriptionService";
import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";
import { StripeService } from "@/lib/stripe/stripeService";
import { verifyWebhookSignature } from "@/lib/stripe/config";

export interface PaymentRequest {
  amount: string;
  currency: string;
  gatewayId: string;
  userId: string;
  metadata?: any;
}

export interface PaymentResponse {
  success: boolean;
  paymentUrl?: string;
  transactionId?: string;
  message?: string;
  error?: string;
  walletPayment?: boolean;
}

export class PaymentService {
  /**
   * Get available payment gateways for users
   */
  static async getAvailablePaymentGateways(options?: { excludeWallet?: boolean }): Promise<any[]> {
    try {
      const gateways = await PaymentGatewayService.getActivePaymentGateways();

      // Filter out wallet gateway if requested
      const filteredGateways = options?.excludeWallet
        ? gateways.filter(gateway => gateway.type !== 'wallet')
        : gateways;

      // Return only necessary information for frontend
      return filteredGateways.map(gateway => ({
        id: gateway.id,
        name: gateway.name,
        displayName: gateway.displayName,
        type: gateway.type,
        depositFee: gateway.depositFee,
        depositFixedFee: gateway.depositFixedFee,
        minDeposit: gateway.minDeposit,
        maxDeposit: gateway.maxDeposit,
        currency: gateway.currency,
        sortOrder: gateway.sortOrder,
        isActive: gateway.isActive,
        // Only include safe config data for frontend
        config: {
          publishableKey: gateway.config?.publishableKey,
          mode: gateway.config?.mode,
          instructions: gateway.config?.instructions,
          accountDetails: gateway.config?.accountDetails,
        }
      }));
    } catch (error) {
      console.error('Error fetching available payment gateways:', error);
      return [];
    }
  }
  // Process payment based on gateway type
  static async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      console.log('Processing payment request:', {
        gatewayId: request.gatewayId,
        amount: request.amount,
        currency: request.currency,
        userId: request.userId
      });

      // Get gateway configuration using the new unified service
      const gateway = await PaymentGatewayService.getPaymentGatewayById(request.gatewayId);

      console.log('Found gateway:', {
        id: gateway?.id,
        name: gateway?.name,
        type: gateway?.type,
        isActive: gateway?.isActive
      });

      if (!gateway) {
        console.error('Gateway not found:', request.gatewayId);
        return {
          success: false,
          error: 'Payment gateway not found'
        };
      }

      if (!gateway.isActive) {
        console.error('Gateway not active:', gateway.name);
        return {
          success: false,
          error: 'Payment gateway is not active'
        };
      }

      // Validate gateway configuration
      const configValidation = PaymentGatewayService.validateGatewayConfig(
        gateway.type as any,
        gateway.config
      );

      console.log('Gateway config validation:', {
        isValid: configValidation.isValid,
        errors: configValidation.errors
      });

      if (!configValidation.isValid) {
        console.error('Gateway config validation failed:', configValidation.errors);
        return {
          success: false,
          error: `Gateway configuration error: ${configValidation.errors.join(', ')}`
        };
      }

      // Route to appropriate payment processor
      switch (gateway.type) {
        case 'stripe':
          return await this.processStripePayment(request, gateway);
        case 'uddoktapay':
          return await this.processUddoktaPayPayment(request, gateway);
        case 'manual':
          return await this.processManualPayment(request, gateway);
        case 'wallet':
          return await this.processWalletPayment(request, gateway);
        default:
          return {
            success: false,
            error: 'Unsupported payment gateway'
          };
      }
    } catch (error: any) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        error: error.message || 'Payment processing failed'
      };
    }
  }

  // Stripe payment processing
  private static async processStripePayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      const { config } = gateway;

      if (!config.publishableKey || !config.secretKey) {
        return {
          success: false,
          error: 'Stripe configuration incomplete'
        };
      }

      // Check if this is a subscription payment
      if (request.metadata?.type === 'subscription_payment' || request.metadata?.type === 'subscription_payment_retry') {
        return await this.processStripeSubscriptionPayment(request, config);
      }

      // Create payment intent for one-time payments (deposits)
      const stripeResponse = await StripeService.createPaymentIntent({
        amount: parseFloat(request.amount),
        currency: request.currency,
        userId: request.userId,
        metadata: {
          transactionId: request.metadata?.transactionId || `stripe_${Date.now()}`,
          type: request.metadata?.type || 'deposit',
          returnUrl: request.metadata?.returnUrl,
          cancelUrl: request.metadata?.cancelUrl,
          ...request.metadata,
        },
        config: config,
      });

      if (!stripeResponse.success) {
        return {
          success: false,
          error: stripeResponse.error || 'Stripe payment failed'
        };
      }

      return {
        success: true,
        transactionId: stripeResponse.paymentIntentId,
        message: 'Payment intent created successfully',
        // Store client secret in metadata for frontend use
        paymentUrl: `/payment/stripe?client_secret=${stripeResponse.clientSecret}&payment_intent=${stripeResponse.paymentIntentId}&amount=${request.amount}&currency=${request.currency}`,
      };
    } catch (error: any) {
      console.error('Stripe payment processing error:', error);
      return {
        success: false,
        error: error.message || 'Stripe payment failed'
      };
    }
  }

  // Stripe subscription payment processing
  private static async processStripeSubscriptionPayment(request: PaymentRequest, config: any): Promise<PaymentResponse> {
    try {
      const { subscriptionId, planId, transactionId } = request.metadata;

      if (!subscriptionId || !planId) {
        return {
          success: false,
          error: 'Missing subscription or plan information'
        };
      }

      // Get user information for customer creation
      // In a real implementation, you'd fetch user details from your database
      const userEmail = request.metadata.userEmail || `user_${request.userId}@example.com`;

      // Create Stripe subscription
      const stripeResponse = await StripeService.createSubscription({
        customerEmail: userEmail,
        priceId: request.metadata.stripePriceId, // This should be stored in the plan
        userId: request.userId,
        metadata: {
          subscriptionId,
          planId,
          transactionId,
          userId: request.userId,
        },
        config: config,
      });

      if (!stripeResponse.success) {
        return {
          success: false,
          error: stripeResponse.error || 'Stripe subscription creation failed'
        };
      }

      return {
        success: true,
        transactionId: stripeResponse.subscriptionId,
        message: 'Subscription created successfully',
        paymentUrl: `/payment/stripe?client_secret=${stripeResponse.clientSecret}&subscription_id=${stripeResponse.subscriptionId}&amount=${request.amount}&currency=${request.currency}`,
      };
    } catch (error: any) {
      console.error('Stripe subscription payment error:', error);
      return {
        success: false,
        error: error.message || 'Stripe subscription payment failed'
      };
    }
  }

  // UddoktaPay payment processing
  private static async processUddoktaPayPayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      const { config } = gateway;

      if (!config.apiKey || !config.storeId) {
        return {
          success: false,
          error: 'UddoktaPay configuration incomplete'
        };
      }

      // Placeholder for UddoktaPay API integration
      // In a real implementation, you would make an API call to UddoktaPay

      return {
        success: true,
        paymentUrl: `${config.apiUrl}/payment?store_id=${config.storeId}&amount=${request.amount}`,
        transactionId: `uddokta_${Date.now()}`,
        message: 'Redirecting to UddoktaPay checkout'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'UddoktaPay payment failed'
      };
    }
  }

  // Manual payment processing
  private static async processManualPayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      const { config } = gateway;
      
      // For manual payments, we just return instructions
      return {
        success: true,
        message: 'Manual payment instructions provided',
        transactionId: `manual_${Date.now()}`,
        // In a real app, you might redirect to a page with payment instructions
        paymentUrl: `/payment/manual?gateway=${gateway.id}&amount=${request.amount}`
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Manual payment setup failed'
      };
    }
  }

  // Wallet payment processing
  private static async processWalletPayment(request: PaymentRequest, gateway: any): Promise<PaymentResponse> {
    try {
      // Get user's wallet balance
      const walletBalance = await WalletService.getOrCreateWallet(request.userId);
      const currentBalance = parseFloat(walletBalance.generalBalance);
      const paymentAmount = parseFloat(request.amount);

      // Check if user has sufficient balance
      if (currentBalance < paymentAmount) {
        return {
          success: false,
          error: `Insufficient wallet balance. Available: $${currentBalance.toFixed(2)}, Required: $${paymentAmount.toFixed(2)}`
        };
      }

      // Process wallet payment for subscription
      if (request.metadata?.type === 'subscription_payment') {
        const { subscriptionId, transactionId, planId } = request.metadata;

        // Create wallet transaction for subscription payment
        const walletTransactionId = await WalletService.createTransaction(request.userId, {
          type: 'internal_transfer',
          amount: request.amount,
          walletType: 'general',
          reference: `subscription_payment_${subscriptionId}`,
          note: `Subscription payment for plan ${planId}`,
          metadata: {
            subscriptionId,
            transactionId,
            planId,
            type: 'subscription_payment'
          }
        });

        // Deduct amount from wallet
        await WalletService.updateWalletBalance(
          request.userId,
          'general',
          request.amount,
          'subtract'
        );

        // Mark wallet transaction as completed
        await WalletService.updateTransactionStatus(walletTransactionId, 'completed');

        // Activate subscription immediately since payment is completed
        if (subscriptionId) {
          await SubscriptionService.activateSubscription(subscriptionId, transactionId);
        }

        return {
          success: true,
          message: 'Payment completed successfully using wallet balance',
          transactionId: walletTransactionId,
          walletPayment: true
        };
      }

      // For other wallet payments (deposits, etc.)
      const walletTransactionId = await WalletService.createTransaction(request.userId, {
        type: 'internal_transfer',
        amount: request.amount,
        walletType: 'general',
        reference: `wallet_payment_${Date.now()}`,
        note: 'Wallet payment',
        metadata: request.metadata
      });

      // Deduct amount from wallet
      await WalletService.updateWalletBalance(
        request.userId,
        'general',
        request.amount,
        'subtract'
      );

      // Mark transaction as completed
      await WalletService.updateTransactionStatus(walletTransactionId, 'completed');

      return {
        success: true,
        message: 'Payment completed successfully using wallet balance',
        transactionId: walletTransactionId,
        walletPayment: true
      };

    } catch (error: any) {
      console.error('Wallet payment error:', error);
      return {
        success: false,
        error: error.message || 'Wallet payment failed'
      };
    }
  }

  // Verify payment (webhook handler)
  static async verifyPayment(gatewayType: string, payload: any): Promise<boolean> {
    try {
      switch (gatewayType) {
        case 'stripe':
          return await this.verifyStripePayment(payload);
        case 'paypal':
          return await this.verifyPayPalPayment(payload);
        case 'uddoktapay':
          return await this.verifyUddoktaPayPayment(payload);
        case 'manual':
          return await this.verifyManualPayment(payload);
        case 'wallet':
          return await this.verifyWalletPayment(payload);
        default:
          return false;
      }
    } catch (error) {
      console.error('Payment verification error:', error);
      return false;
    }
  }

  private static async verifyStripePayment(payload: any): Promise<boolean> {
    try {
      // This method is called from webhook handler which should already have verified the signature
      // The payload here is the verified Stripe event object
      const event = payload;

      console.log('Processing Stripe webhook event:', event.type);

      switch (event.type) {
        case 'payment_intent.succeeded':
          const paymentIntent = event.data.object;
          console.log('Payment succeeded:', paymentIntent.id);

          // Update transaction status to completed
          if (paymentIntent.metadata?.transactionId) {
            await WalletService.updateTransactionStatus(
              paymentIntent.metadata.transactionId,
              'completed',
              paymentIntent.id
            );
          }

          return true;

        case 'payment_intent.payment_failed':
          const failedPayment = event.data.object;
          console.log('Payment failed:', failedPayment.id);

          // Update transaction status to failed
          if (failedPayment.metadata?.transactionId) {
            await WalletService.updateTransactionStatus(
              failedPayment.metadata.transactionId,
              'failed'
            );
          }

          return true;

        case 'invoice.payment_succeeded':
          // Handle subscription payment success
          const invoice = event.data.object;
          console.log('Subscription payment succeeded:', invoice.id);

          if (invoice.subscription_details?.metadata?.subscriptionId) {
            // Update subscription transaction status
            await SubscriptionService.updateTransactionStatus(
              invoice.subscription_details.metadata.subscriptionId,
              'completed',
              { gatewayTransactionId: invoice.id }
            );
          }

          return true;

        case 'invoice.payment_failed':
          // Handle subscription payment failure
          const failedInvoice = event.data.object;
          console.log('Subscription payment failed:', failedInvoice.id);

          if (failedInvoice.subscription_details?.metadata?.subscriptionId) {
            await SubscriptionService.updateTransactionStatus(
              failedInvoice.subscription_details.metadata.subscriptionId,
              'failed'
            );
          }

          return true;

        default:
          console.log('Unhandled Stripe event type:', event.type);
          return true;
      }
    } catch (error) {
      console.error('Stripe webhook verification error:', error);
      return false;
    }
  }

  private static async verifyPayPalPayment(payload: any): Promise<boolean> {
    // Implement PayPal webhook verification
    return true; // Placeholder
  }

  private static async verifyUddoktaPayPayment(payload: any): Promise<boolean> {
    // Implement UddoktaPay webhook verification
    return true; // Placeholder
  }

  private static async verifyManualPayment(payload: any): Promise<boolean> {
    // Manual payments require admin approval
    return false; // Always requires manual verification
  }

  private static async verifyWalletPayment(payload: any): Promise<boolean> {
    // Wallet payments are processed immediately, so they're always verified
    return true;
  }
}
