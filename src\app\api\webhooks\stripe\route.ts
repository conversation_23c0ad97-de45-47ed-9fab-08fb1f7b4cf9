import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { verifyWebhookSignature } from "@/lib/stripe/config";
import { PaymentService } from "@/lib/payment/paymentService";
import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";
import { WalletService } from "@/lib/wallet/walletService";
import { SubscriptionService } from "@/lib/subscription/subscriptionService";

export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      console.error('Missing Stripe signature');
      return NextResponse.json(
        { error: 'Missing Stripe signature' },
        { status: 400 }
      );
    }

    // Get Stripe webhook secret from environment or gateway config
    let webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    
    // If no global webhook secret, try to get it from the first active Stripe gateway
    if (!webhookSecret) {
      const stripeGateways = await PaymentGatewayService.getActivePaymentGateways();
      const stripeGateway = stripeGateways.find(g => g.type === 'stripe');
      
      if (stripeGateway?.config?.webhookSecret) {
        webhookSecret = stripeGateway.config.webhookSecret;
      } else {
        console.error('No Stripe webhook secret found');
        return NextResponse.json(
          { error: 'Webhook secret not configured' },
          { status: 400 }
        );
      }
    }

    // Verify webhook signature
    const event = verifyWebhookSignature(body, signature, webhookSecret);
    
    if (!event) {
      console.error('Invalid Stripe webhook signature');
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    console.log('Processing Stripe webhook event:', event.type, event.id);

    // Handle different event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event);
        break;

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event);
        break;

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event);
        break;

      default:
        console.log('Unhandled Stripe event type:', event.type);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Stripe webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentIntentSucceeded(event: any) {
  try {
    const paymentIntent = event.data.object;
    const { transactionId, type } = paymentIntent.metadata;

    console.log('Payment intent succeeded:', paymentIntent.id, 'Transaction:', transactionId);

    if (type === 'deposit') {
      // Handle deposit completion
      if (transactionId) {
        await WalletService.updateTransactionStatus(
          transactionId,
          'completed',
          paymentIntent.id
        );
        
        // Complete the deposit to add funds to wallet
        await WalletService.completeDeposit(transactionId);
      }
    } else if (type === 'subscription_payment' || type === 'subscription_payment_retry') {
      // Handle subscription payment completion
      if (transactionId) {
        await SubscriptionService.updateTransactionStatus(
          transactionId,
          'completed',
          { gatewayTransactionId: paymentIntent.id }
        );
      }
    }
  } catch (error) {
    console.error('Error handling payment intent succeeded:', error);
  }
}

async function handlePaymentIntentFailed(event: any) {
  try {
    const paymentIntent = event.data.object;
    const { transactionId, type } = paymentIntent.metadata;

    console.log('Payment intent failed:', paymentIntent.id, 'Transaction:', transactionId);

    if (type === 'deposit') {
      // Handle deposit failure
      if (transactionId) {
        await WalletService.updateTransactionStatus(transactionId, 'failed');
      }
    } else if (type === 'subscription_payment' || type === 'subscription_payment_retry') {
      // Handle subscription payment failure
      if (transactionId) {
        await SubscriptionService.updateTransactionStatus(transactionId, 'failed');
      }
    }
  } catch (error) {
    console.error('Error handling payment intent failed:', error);
  }
}

async function handleInvoicePaymentSucceeded(event: any) {
  try {
    const invoice = event.data.object;
    const subscription = invoice.subscription;

    console.log('Invoice payment succeeded:', invoice.id, 'Subscription:', subscription);

    // Handle subscription renewal payment success
    if (subscription && invoice.subscription_details?.metadata) {
      const { subscriptionId } = invoice.subscription_details.metadata;
      
      if (subscriptionId) {
        // Create a new transaction record for the renewal
        const transactionId = await SubscriptionService.createRenewalTransaction(
          subscriptionId,
          invoice.amount_paid / 100, // Convert from cents
          invoice.currency,
          'stripe'
        );

        // Mark the transaction as completed
        await SubscriptionService.updateTransactionStatus(
          transactionId,
          'completed',
          { gatewayTransactionId: invoice.id }
        );
      }
    }
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleInvoicePaymentFailed(event: any) {
  try {
    const invoice = event.data.object;
    const subscription = invoice.subscription;

    console.log('Invoice payment failed:', invoice.id, 'Subscription:', subscription);

    // Handle subscription renewal payment failure
    if (subscription && invoice.subscription_details?.metadata) {
      const { subscriptionId } = invoice.subscription_details.metadata;
      
      if (subscriptionId) {
        // Create a failed transaction record
        const transactionId = await SubscriptionService.createRenewalTransaction(
          subscriptionId,
          invoice.amount_due / 100, // Convert from cents
          invoice.currency,
          'stripe'
        );

        // Mark the transaction as failed
        await SubscriptionService.updateTransactionStatus(transactionId, 'failed');
      }
    }
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
  }
}

async function handleSubscriptionCreated(event: any) {
  try {
    const subscription = event.data.object;
    console.log('Subscription created:', subscription.id);
    
    // Handle subscription creation if needed
    // This is typically handled when the subscription is first created via API
  } catch (error) {
    console.error('Error handling subscription created:', error);
  }
}

async function handleSubscriptionUpdated(event: any) {
  try {
    const subscription = event.data.object;
    console.log('Subscription updated:', subscription.id, 'Status:', subscription.status);
    
    // Handle subscription status changes
    if (subscription.metadata?.subscriptionId) {
      const { subscriptionId } = subscription.metadata;
      
      // Update subscription status based on Stripe subscription status
      if (subscription.status === 'active') {
        await SubscriptionService.activateSubscription(subscriptionId);
      } else if (subscription.status === 'canceled' || subscription.status === 'incomplete_expired') {
        await SubscriptionService.cancelSubscription(subscriptionId);
      }
    }
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionDeleted(event: any) {
  try {
    const subscription = event.data.object;
    console.log('Subscription deleted:', subscription.id);
    
    // Handle subscription cancellation
    if (subscription.metadata?.subscriptionId) {
      const { subscriptionId } = subscription.metadata;
      await SubscriptionService.cancelSubscription(subscriptionId);
    }
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}
