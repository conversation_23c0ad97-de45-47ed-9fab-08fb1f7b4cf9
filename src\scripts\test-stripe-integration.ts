#!/usr/bin/env tsx

/**
 * Stripe Integration Test Script
 * 
 * This script validates the Stripe payment gateway integration by testing:
 * - Configuration validation
 * - Payment intent creation
 * - Webhook signature verification
 * - Database operations
 * - Error handling
 */

import { PaymentGatewayService } from '@/lib/payment/paymentGatewayService';
import { PaymentService } from '@/lib/payment/paymentService';
import { StripeService } from '@/lib/stripe/stripeService';
import { validateStripeConfig, verifyWebhookSignature } from '@/lib/stripe/config';
import { StripeErrorHandler } from '@/lib/stripe/errorHandler';
import { db } from '@/lib/db';
import { paymentGateways } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration: number;
}

class StripeIntegrationTester {
  private results: TestResult[] = [];
  private testGatewayId: string | null = null;

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Stripe Integration Tests...\n');

    try {
      // Configuration Tests
      await this.testConfigurationValidation();
      await this.testEnvironmentVariables();
      
      // Gateway Tests
      await this.testGatewayCreation();
      await this.testGatewayConfiguration();
      
      // Payment Tests
      await this.testPaymentIntentCreation();
      await this.testPaymentProcessing();
      
      // Webhook Tests
      await this.testWebhookSignatureVerification();
      
      // Error Handling Tests
      await this.testErrorHandling();
      
      // Cleanup
      await this.cleanup();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }

    this.printResults();
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: true,
        message: 'Passed',
        duration
      });
      console.log(`✅ ${name} (${duration}ms)`);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: false,
        message: error.message || 'Unknown error',
        duration
      });
      console.log(`❌ ${name} (${duration}ms): ${error.message}`);
    }
  }

  private async testConfigurationValidation(): Promise<void> {
    await this.runTest('Configuration Validation', async () => {
      // Test valid configuration
      const validConfig = {
        publishableKey: 'pk_test_valid_key',
        secretKey: 'sk_test_valid_key',
        webhookSecret: 'whsec_valid_secret',
        mode: 'test' as const
      };

      const validResult = validateStripeConfig(validConfig);
      if (!validResult.isValid) {
        throw new Error(`Valid config failed validation: ${validResult.errors.join(', ')}`);
      }

      // Test invalid configuration
      const invalidConfig = {
        publishableKey: 'invalid_key',
        secretKey: 'invalid_key',
        webhookSecret: 'invalid_secret',
        mode: 'invalid' as any
      };

      const invalidResult = validateStripeConfig(invalidConfig);
      if (invalidResult.isValid) {
        throw new Error('Invalid config passed validation');
      }
    });
  }

  private async testEnvironmentVariables(): Promise<void> {
    await this.runTest('Environment Variables', async () => {
      const requiredVars = [
        'STRIPE_PUBLISHABLE_KEY',
        'STRIPE_SECRET_KEY',
        'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
      ];

      for (const varName of requiredVars) {
        if (!process.env[varName]) {
          throw new Error(`Missing environment variable: ${varName}`);
        }
      }

      // Validate key formats
      const pubKey = process.env.STRIPE_PUBLISHABLE_KEY!;
      const secretKey = process.env.STRIPE_SECRET_KEY!;

      if (!pubKey.startsWith('pk_')) {
        throw new Error('Invalid publishable key format');
      }

      if (!secretKey.startsWith('sk_')) {
        throw new Error('Invalid secret key format');
      }
    });
  }

  private async testGatewayCreation(): Promise<void> {
    await this.runTest('Gateway Creation', async () => {
      const gatewayData = {
        name: `test_stripe_${Date.now()}`,
        displayName: 'Test Stripe Gateway',
        type: 'stripe' as const,
        isActive: false,
        config: {
          publishableKey: process.env.STRIPE_PUBLISHABLE_KEY!,
          secretKey: process.env.STRIPE_SECRET_KEY!,
          webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_test',
          mode: 'test'
        },
        depositFee: '2.90',
        depositFixedFee: '0.30',
        minDeposit: '1.00',
        maxDeposit: '10000.00',
        currency: 'USD',
        sortOrder: 999
      };

      this.testGatewayId = await PaymentGatewayService.createPaymentGateway(gatewayData);
      
      if (!this.testGatewayId) {
        throw new Error('Failed to create test gateway');
      }
    });
  }

  private async testGatewayConfiguration(): Promise<void> {
    await this.runTest('Gateway Configuration', async () => {
      if (!this.testGatewayId) {
        throw new Error('No test gateway available');
      }

      const gateway = await PaymentGatewayService.getPaymentGatewayById(this.testGatewayId);
      
      if (!gateway) {
        throw new Error('Failed to retrieve test gateway');
      }

      if (gateway.type !== 'stripe') {
        throw new Error('Gateway type mismatch');
      }

      // Test configuration validation
      const validation = PaymentGatewayService.validateGatewayConfig(gateway.type, gateway.config);
      
      if (!validation.isValid) {
        throw new Error(`Gateway config validation failed: ${validation.errors.join(', ')}`);
      }
    });
  }

  private async testPaymentIntentCreation(): Promise<void> {
    await this.runTest('Payment Intent Creation', async () => {
      if (!this.testGatewayId) {
        throw new Error('No test gateway available');
      }

      const gateway = await PaymentGatewayService.getPaymentGatewayById(this.testGatewayId);
      
      if (!gateway) {
        throw new Error('Test gateway not found');
      }

      const paymentRequest = {
        amount: 10.00,
        currency: 'USD',
        userId: 'test_user_' + Date.now(),
        metadata: {
          transactionId: 'test_' + Date.now(),
          type: 'deposit'
        },
        config: gateway.config
      };

      const result = await StripeService.createPaymentIntent(paymentRequest);
      
      if (!result.success) {
        throw new Error(`Payment intent creation failed: ${result.error}`);
      }

      if (!result.paymentIntentId || !result.clientSecret) {
        throw new Error('Missing payment intent data');
      }
    });
  }

  private async testPaymentProcessing(): Promise<void> {
    await this.runTest('Payment Processing', async () => {
      if (!this.testGatewayId) {
        throw new Error('No test gateway available');
      }

      const paymentRequest = {
        amount: '5.00',
        currency: 'USD',
        gatewayId: this.testGatewayId,
        userId: 'test_user_' + Date.now(),
        metadata: {
          type: 'deposit',
          transactionId: 'test_' + Date.now()
        }
      };

      const result = await PaymentService.processPayment(paymentRequest);
      
      if (!result.success) {
        throw new Error(`Payment processing failed: ${result.error}`);
      }

      if (!result.paymentUrl) {
        throw new Error('Missing payment URL');
      }
    });
  }

  private async testWebhookSignatureVerification(): Promise<void> {
    await this.runTest('Webhook Signature Verification', async () => {
      const testPayload = JSON.stringify({
        id: 'evt_test',
        type: 'payment_intent.succeeded',
        data: { object: { id: 'pi_test' } }
      });

      const testSecret = 'whsec_test_secret';
      const testSignature = 'invalid_signature';

      // This should fail with invalid signature
      const result = verifyWebhookSignature(testPayload, testSignature, testSecret);
      
      if (result !== null) {
        throw new Error('Webhook verification should have failed with invalid signature');
      }

      // Test with missing signature
      try {
        verifyWebhookSignature(testPayload, '', testSecret);
        throw new Error('Should have thrown error with empty signature');
      } catch (error) {
        // Expected to fail
      }
    });
  }

  private async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling', async () => {
      // Test error code mapping
      const testError = { code: 'card_declined', message: 'Your card was declined.' };
      const errorDetails = StripeErrorHandler.getErrorDetails('card_declined');
      
      if (!errorDetails) {
        throw new Error('Failed to get error details');
      }

      if (errorDetails.code !== 'card_declined') {
        throw new Error('Error code mismatch');
      }

      if (!errorDetails.userMessage) {
        throw new Error('Missing user message');
      }

      // Test unknown error handling
      const unknownError = StripeErrorHandler.getErrorDetails('unknown_error_code');
      
      if (unknownError.code !== 'unknown_error') {
        throw new Error('Should fallback to unknown error');
      }
    });
  }

  private async cleanup(): Promise<void> {
    await this.runTest('Cleanup', async () => {
      if (this.testGatewayId) {
        await PaymentGatewayService.deletePaymentGateway(this.testGatewayId);
        console.log(`🧹 Cleaned up test gateway: ${this.testGatewayId}`);
      }
    });
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const total = this.results.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed} ✅`);
    console.log(`Failed: ${failed} ❌`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`  - ${r.name}: ${r.message}`));
    }
    
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`\nTotal Duration: ${totalDuration}ms`);
    
    if (failed === 0) {
      console.log('\n🎉 All tests passed! Stripe integration is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the errors above.');
      process.exit(1);
    }
  }
}

// Validation script for quick configuration check
export async function validateStripeSetup(): Promise<boolean> {
  console.log('🔍 Validating Stripe Setup...\n');

  try {
    // Check environment variables
    const requiredVars = [
      'STRIPE_PUBLISHABLE_KEY',
      'STRIPE_SECRET_KEY',
      'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
    ];

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        console.log(`❌ Missing environment variable: ${varName}`);
        return false;
      }
      console.log(`✅ ${varName} is set`);
    }

    // Check for active Stripe gateways
    const stripeGateways = await PaymentGatewayService.getActivePaymentGateways();
    const activeStripeGateway = stripeGateways.find(g => g.type === 'stripe');

    if (!activeStripeGateway) {
      console.log('❌ No active Stripe gateway found in admin panel');
      return false;
    }
    console.log(`✅ Active Stripe gateway found: ${activeStripeGateway.displayName}`);

    // Validate gateway configuration
    const validation = PaymentGatewayService.validateGatewayConfig(
      activeStripeGateway.type,
      activeStripeGateway.config
    );

    if (!validation.isValid) {
      console.log(`❌ Gateway configuration invalid: ${validation.errors.join(', ')}`);
      return false;
    }
    console.log('✅ Gateway configuration is valid');

    console.log('\n🎉 Stripe setup validation passed!');
    return true;

  } catch (error: any) {
    console.log(`❌ Validation failed: ${error.message}`);
    return false;
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--validate-only')) {
    validateStripeSetup().then(success => {
      process.exit(success ? 0 : 1);
    });
  } else {
    const tester = new StripeIntegrationTester();
    tester.runAllTests().catch(console.error);
  }
}

export { StripeIntegrationTester };
