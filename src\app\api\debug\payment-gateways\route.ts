import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get all payment gateways
    const allGateways = await PaymentGatewayService.getAllPaymentGateways();
    const activeGateways = await PaymentGatewayService.getActivePaymentGateways();

    // Validate each gateway configuration
    const gatewayValidation = allGateways.map(gateway => {
      const validation = PaymentGatewayService.validateGatewayConfig(
        gateway.type as any,
        gateway.config
      );

      return {
        id: gateway.id,
        name: gateway.name,
        displayName: gateway.displayName,
        type: gateway.type,
        isActive: gateway.isActive,
        isValid: validation.isValid,
        errors: validation.errors,
        config: {
          hasPublishableKey: !!gateway.config?.publishableKey,
          hasSecretKey: !!gateway.config?.secretKey,
          hasWebhookSecret: !!gateway.config?.webhookSecret,
          hasClientId: !!gateway.config?.clientId,
          hasClientSecret: !!gateway.config?.clientSecret,
          hasApiKey: !!gateway.config?.apiKey,
          hasInstructions: !!gateway.config?.instructions,
          mode: gateway.config?.mode,
        }
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        totalGateways: allGateways.length,
        activeGateways: activeGateways.length,
        gateways: gatewayValidation,
      },
    });
  } catch (error: any) {
    console.error("Error debugging payment gateways:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to debug payment gateways",
        error: error.message
      },
      { status: 500 }
    );
  }
}
