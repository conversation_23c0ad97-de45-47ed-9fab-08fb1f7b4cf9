import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";
import { paymentGatewaySchema } from "@/lib/wallet/validation";
import { z } from "zod";

// Get payment gateway by ID
export async function GET(
  req: Request,
  context: { params: Promise<{ gatewayId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { gatewayId } = params;

    const gateway = await PaymentGatewayService.getPaymentGatewayById(gatewayId);

    if (!gateway) {
      return NextResponse.json(
        { message: "Payment gateway not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: gateway,
    });
  } catch (error: any) {
    console.error("Error fetching payment gateway:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch payment gateway" 
      },
      { status: 500 }
    );
  }
}

// Update payment gateway
export async function PUT(
  req: Request,
  context: { params: Promise<{ gatewayId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { gatewayId } = params;

    const body = await req.json();
    const validatedData = paymentGatewaySchema.parse(body);

    // Validate gateway-specific configuration
    const configValidation = PaymentGatewayService.validateGatewayConfig(
      validatedData.type, 
      validatedData.config
    );

    if (!configValidation.isValid) {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid gateway configuration",
          errors: configValidation.errors
        },
        { status: 400 }
      );
    }

    await PaymentGatewayService.updatePaymentGateway(gatewayId, validatedData);

    return NextResponse.json({
      success: true,
      message: "Payment gateway updated successfully",
    });
  } catch (error: any) {
    console.error("Error updating payment gateway:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          message: "Validation error",
          errors: error.errors.map(e => e.message)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to update payment gateway" 
      },
      { status: 500 }
    );
  }
}

// Delete payment gateway
export async function DELETE(
  req: Request,
  context: { params: Promise<{ gatewayId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { gatewayId } = params;

    await PaymentGatewayService.deletePaymentGateway(gatewayId);

    return NextResponse.json({
      success: true,
      message: "Payment gateway deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting payment gateway:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to delete payment gateway" 
      },
      { status: 500 }
    );
  }
}
