import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";
import { PaymentService } from "@/lib/payment/paymentService";

/**
 * Test script for the unified payment gateway system
 * This script validates all the functionality of the new payment gateway system
 */

async function testPaymentGatewaySystem() {
  console.log('🧪 Testing Payment Gateway System...\n');

  try {
    // Test 1: Get all payment gateways
    console.log('1️⃣ Testing PaymentGatewayService.getAllPaymentGateways()');
    const allGateways = await PaymentGatewayService.getAllPaymentGateways();
    console.log(`   ✅ Found ${allGateways.length} payment gateways`);
    allGateways.forEach(gateway => {
      console.log(`   - ${gateway.displayName} (${gateway.type}) - ${gateway.isActive ? 'Active' : 'Inactive'}`);
    });
    console.log('');

    // Test 2: Get active payment gateways only
    console.log('2️⃣ Testing PaymentGatewayService.getActivePaymentGateways()');
    const activeGateways = await PaymentGatewayService.getActivePaymentGateways();
    console.log(`   ✅ Found ${activeGateways.length} active payment gateways`);
    activeGateways.forEach(gateway => {
      console.log(`   - ${gateway.displayName} (${gateway.type})`);
    });
    console.log('');

    // Test 3: Get gateways by type
    console.log('3️⃣ Testing PaymentGatewayService.getPaymentGatewaysByType()');
    const stripeGateways = await PaymentGatewayService.getPaymentGatewaysByType('stripe');
    console.log(`   ✅ Found ${stripeGateways.length} Stripe gateways`);
    const manualGateways = await PaymentGatewayService.getPaymentGatewaysByType('manual');
    console.log(`   ✅ Found ${manualGateways.length} manual gateways`);
    console.log('');

    // Test 4: Test gateway configuration validation
    console.log('4️⃣ Testing PaymentGatewayService.validateGatewayConfig()');
    
    // Test Stripe validation
    const stripeConfigValid = PaymentGatewayService.validateGatewayConfig('stripe', {
      publishableKey: 'pk_test_123',
      secretKey: 'sk_test_123',
      webhookSecret: 'whsec_123'
    });
    console.log(`   ✅ Stripe config validation (valid): ${stripeConfigValid.isValid}`);

    const stripeConfigInvalid = PaymentGatewayService.validateGatewayConfig('stripe', {
      publishableKey: 'pk_test_123'
      // Missing secretKey and webhookSecret
    });
    console.log(`   ✅ Stripe config validation (invalid): ${stripeConfigInvalid.isValid}, errors: ${stripeConfigInvalid.errors.join(', ')}`);

    // Test Manual validation
    const manualConfigValid = PaymentGatewayService.validateGatewayConfig('manual', {
      instructions: 'Please transfer to our bank account'
    });
    console.log(`   ✅ Manual config validation (valid): ${manualConfigValid.isValid}`);
    console.log('');

    // Test 5: Test PaymentService.getAvailablePaymentGateways()
    console.log('5️⃣ Testing PaymentService.getAvailablePaymentGateways()');
    const availableGateways = await PaymentService.getAvailablePaymentGateways();
    console.log(`   ✅ Found ${availableGateways.length} available gateways for users`);
    availableGateways.forEach(gateway => {
      console.log(`   - ${gateway.displayName} (${gateway.type}) - Fee: ${gateway.depositFee}% + $${gateway.depositFixedFee}`);
    });
    console.log('');

    // Test 6: Test gateway creation (and cleanup)
    console.log('6️⃣ Testing PaymentGatewayService.createPaymentGateway()');
    const testGatewayData = {
      name: 'test_gateway_' + Date.now(),
      displayName: 'Test Gateway',
      type: 'manual' as const,
      isActive: false,
      config: {
        instructions: 'This is a test gateway',
        accountDetails: 'Test account details'
      },
      depositFee: '1.50',
      depositFixedFee: '0.50',
      minDeposit: '5.00',
      maxDeposit: '1000.00',
      currency: 'USD',
      sortOrder: 999
    };

    const testGatewayId = await PaymentGatewayService.createPaymentGateway(testGatewayData);
    console.log(`   ✅ Created test gateway with ID: ${testGatewayId}`);

    // Test 7: Test gateway retrieval by ID
    console.log('7️⃣ Testing PaymentGatewayService.getPaymentGatewayById()');
    const retrievedGateway = await PaymentGatewayService.getPaymentGatewayById(testGatewayId);
    console.log(`   ✅ Retrieved gateway: ${retrievedGateway?.displayName}`);

    // Test 8: Test gateway update
    console.log('8️⃣ Testing PaymentGatewayService.updatePaymentGateway()');
    await PaymentGatewayService.updatePaymentGateway(testGatewayId, {
      displayName: 'Updated Test Gateway',
      isActive: true
    });
    const updatedGateway = await PaymentGatewayService.getPaymentGatewayById(testGatewayId);
    console.log(`   ✅ Updated gateway: ${updatedGateway?.displayName}, Active: ${updatedGateway?.isActive}`);

    // Test 9: Test gateway status toggle
    console.log('9️⃣ Testing PaymentGatewayService.togglePaymentGatewayStatus()');
    await PaymentGatewayService.togglePaymentGatewayStatus(testGatewayId);
    const toggledGateway = await PaymentGatewayService.getPaymentGatewayById(testGatewayId);
    console.log(`   ✅ Toggled gateway status: ${toggledGateway?.isActive}`);

    // Test 10: Test gateway deletion (cleanup)
    console.log('🔟 Testing PaymentGatewayService.deletePaymentGateway()');
    await PaymentGatewayService.deletePaymentGateway(testGatewayId);
    const deletedGateway = await PaymentGatewayService.getPaymentGatewayById(testGatewayId);
    console.log(`   ✅ Deleted gateway (should be null): ${deletedGateway}`);
    console.log('');

    // Test 11: Test supported gateway types
    console.log('1️⃣1️⃣ Testing PaymentGatewayService.getSupportedGatewayTypes()');
    const supportedTypes = PaymentGatewayService.getSupportedGatewayTypes();
    console.log(`   ✅ Found ${supportedTypes.length} supported gateway types:`);
    supportedTypes.forEach(type => {
      console.log(`   - ${type.label}: ${type.description}`);
    });
    console.log('');

    console.log('🎉 All tests passed! Payment Gateway System is working correctly.\n');

    // Summary
    console.log('📊 Test Summary:');
    console.log(`   - Total gateways in system: ${allGateways.length}`);
    console.log(`   - Active gateways: ${activeGateways.length}`);
    console.log(`   - Available for users: ${availableGateways.length}`);
    console.log(`   - Supported types: ${supportedTypes.length}`);
    console.log(`   - CRUD operations: ✅ All working`);
    console.log(`   - Configuration validation: ✅ Working`);
    console.log(`   - Service integration: ✅ Working`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testPaymentGatewaySystem()
    .then(() => {
      console.log('✅ All tests completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Tests failed:', error);
      process.exit(1);
    });
}

export { testPaymentGatewaySystem };
