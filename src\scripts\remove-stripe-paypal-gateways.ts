import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";

async function removeStripePayPalGateways() {
  console.log('🗑️ Removing Stripe and PayPal Payment Gateways\n');

  try {
    // Get all payment gateways
    const allGateways = await PaymentGatewayService.getAllPaymentGateways();
    console.log(`📋 Found ${allGateways.length} total payment gateways`);

    // Find Stripe and PayPal gateways
    const stripeGateways = allGateways.filter(g => g.type === 'stripe');
    const paypalGateways = allGateways.filter(g => g.type === 'paypal');

    console.log(`🔍 Found ${stripeGateways.length} Stripe gateways`);
    console.log(`🔍 Found ${paypalGateways.length} PayPal gateways`);

    // Remove Stripe gateways
    if (stripeGateways.length > 0) {
      console.log('\n🗑️ Removing Stripe gateways:');
      for (const gateway of stripeGateways) {
        console.log(`   - Removing: ${gateway.displayName} (${gateway.id})`);
        await PaymentGatewayService.deletePaymentGateway(gateway.id);
        console.log(`   ✅ Deleted: ${gateway.displayName}`);
      }
    } else {
      console.log('\n✅ No Stripe gateways found to remove');
    }

    // Remove PayPal gateways
    if (paypalGateways.length > 0) {
      console.log('\n🗑️ Removing PayPal gateways:');
      for (const gateway of paypalGateways) {
        console.log(`   - Removing: ${gateway.displayName} (${gateway.id})`);
        await PaymentGatewayService.deletePaymentGateway(gateway.id);
        console.log(`   ✅ Deleted: ${gateway.displayName}`);
      }
    } else {
      console.log('\n✅ No PayPal gateways found to remove');
    }

    // Verify removal
    console.log('\n🔍 Verifying removal:');
    const remainingGateways = await PaymentGatewayService.getAllPaymentGateways();
    const remainingStripe = remainingGateways.filter(g => g.type === 'stripe');
    const remainingPaypal = remainingGateways.filter(g => g.type === 'paypal');

    console.log(`   - Remaining Stripe gateways: ${remainingStripe.length}`);
    console.log(`   - Remaining PayPal gateways: ${remainingPaypal.length}`);
    console.log(`   - Total remaining gateways: ${remainingGateways.length}`);

    if (remainingStripe.length === 0 && remainingPaypal.length === 0) {
      console.log('\n🎉 Successfully removed all Stripe and PayPal gateways!');
    } else {
      console.log('\n⚠️ Some gateways may not have been removed:');
      if (remainingStripe.length > 0) {
        remainingStripe.forEach(g => console.log(`   - Stripe: ${g.displayName} (${g.id})`));
      }
      if (remainingPaypal.length > 0) {
        remainingPaypal.forEach(g => console.log(`   - PayPal: ${g.displayName} (${g.id})`));
      }
    }

    console.log('\n📋 Remaining payment gateways:');
    if (remainingGateways.length > 0) {
      remainingGateways.forEach(gateway => {
        console.log(`   - ${gateway.displayName} (${gateway.type}) - ${gateway.isActive ? 'Active' : 'Inactive'}`);
      });
    } else {
      console.log('   No payment gateways remaining');
    }

  } catch (error: any) {
    console.error('❌ Error removing gateways:', error.message);
    console.error(error.stack);
  }
}

// Run the script if this file is executed directly
if (require.main === module) {
  removeStripePayPalGateways().catch(console.error);
}

export { removeStripePayPalGateways };
