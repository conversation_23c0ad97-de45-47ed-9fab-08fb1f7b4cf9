import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";

// Toggle payment gateway active status
export async function POST(
  req: Request,
  context: { params: Promise<{ gatewayId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { gatewayId } = params;

    await PaymentGatewayService.togglePaymentGatewayStatus(gatewayId);

    return NextResponse.json({
      success: true,
      message: "Payment gateway status updated successfully",
    });
  } catch (error: any) {
    console.error("Error toggling payment gateway status:", error);
    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to update payment gateway status" 
      },
      { status: 500 }
    );
  }
}
