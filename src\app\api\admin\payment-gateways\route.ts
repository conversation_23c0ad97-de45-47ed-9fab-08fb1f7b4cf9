import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { PaymentGatewayService } from "@/lib/payment/paymentGatewayService";
import { paymentGatewaySchema } from "@/lib/wallet/validation";
import { z } from "zod";

// Get all payment gateways (admin only)
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const gateways = await PaymentGatewayService.getAllPaymentGateways();

    return NextResponse.json({
      success: true,
      data: gateways,
    });
  } catch (error: any) {
    console.error("Error fetching payment gateways:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "Failed to fetch payment gateways" 
      },
      { status: 500 }
    );
  }
}

// Create new payment gateway
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    
    // Validate the request data
    const validatedData = paymentGatewaySchema.parse(body);

    // Validate gateway-specific configuration
    const configValidation = PaymentGatewayService.validateGatewayConfig(
      validatedData.type, 
      validatedData.config
    );

    if (!configValidation.isValid) {
      return NextResponse.json(
        { 
          success: false,
          message: "Invalid gateway configuration",
          errors: configValidation.errors
        },
        { status: 400 }
      );
    }

    const gatewayId = await PaymentGatewayService.createPaymentGateway(validatedData);

    return NextResponse.json({
      success: true,
      message: "Payment gateway created successfully",
      data: { id: gatewayId },
    });
  } catch (error: any) {
    console.error("Error creating payment gateway:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          message: "Validation error",
          errors: error.errors.map(e => e.message)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: error.message || "Failed to create payment gateway" 
      },
      { status: 500 }
    );
  }
}
