import { db } from "@/lib/db";
import { paymentGateways } from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

/**
 * Setup script for payment gateways
 * This script ensures the payment_gateways table is properly configured
 * and creates default gateway entries if needed
 */

async function setupPaymentGateways() {
  try {
    console.log('🚀 Setting up payment gateways...');

    // Ensure the payment_gateways table has all required enum values
    try {
      await db.execute(sql`
        ALTER TABLE payment_gateways
        MODIFY COLUMN type ENUM(
          'stripe',
          'paypal',
          'sslcommerz',
          'bkash',
          'nagad',
          'rocket',
          'bank',
          'uddoktapay',
          'manual',
          'wallet'
        ) NOT NULL
      `);
      console.log('✅ Updated payment gateway enum types');
    } catch (enumError) {
      console.log('ℹ️ Enum types already up to date');
    }

    // Check if wallet gateway exists
    const existingWallet = await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.name, 'wallet')
    });

    if (!existingWallet) {
      // Create default wallet payment gateway
      const walletGateway = {
        id: uuidv4(),
        name: 'wallet',
        displayName: 'Wallet Balance',
        type: 'wallet' as const,
        isActive: true,
        config: {
          description: 'Pay using your wallet balance',
          instantPayment: true,
          requiresBalance: true,
        },
        depositFee: '0.00',
        depositFixedFee: '0.00',
        minDeposit: '0.01',
        maxDeposit: '999999.99',
        currency: 'USD',
        sortOrder: 1
      };

      await db.insert(paymentGateways).values(walletGateway);
      console.log('✅ Created default wallet payment gateway');
    } else {
      console.log('ℹ️ Wallet payment gateway already exists');
    }

    // Create sample Stripe gateway (inactive by default)
    const existingStripe = await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.name, 'stripe_main')
    });

    if (!existingStripe) {
      const stripeGateway = {
        id: uuidv4(),
        name: 'stripe_main',
        displayName: 'Credit/Debit Card',
        type: 'stripe' as const,
        isActive: false, // Inactive until configured
        config: {
          publishableKey: '',
          secretKey: '',
          webhookSecret: '',
          mode: 'test'
        },
        depositFee: '2.90',
        depositFixedFee: '0.30',
        minDeposit: '1.00',
        maxDeposit: '10000.00',
        currency: 'USD',
        sortOrder: 2
      };

      await db.insert(paymentGateways).values(stripeGateway);
      console.log('✅ Created sample Stripe payment gateway (inactive)');
    } else {
      console.log('ℹ️ Stripe payment gateway already exists');
    }

    // Create sample manual gateway
    const existingManual = await db.query.paymentGateways.findFirst({
      where: eq(paymentGateways.name, 'bank_transfer')
    });

    if (!existingManual) {
      const manualGateway = {
        id: uuidv4(),
        name: 'bank_transfer',
        displayName: 'Bank Transfer',
        type: 'manual' as const,
        isActive: false, // Inactive until configured
        config: {
          instructions: 'Please transfer the amount to our bank account and provide the transaction reference.',
          accountDetails: 'Bank: Example Bank\nAccount: *********0\nRouting: *********'
        },
        depositFee: '0.00',
        depositFixedFee: '0.00',
        minDeposit: '10.00',
        maxDeposit: '50000.00',
        currency: 'USD',
        sortOrder: 3
      };

      await db.insert(paymentGateways).values(manualGateway);
      console.log('✅ Created sample manual payment gateway (inactive)');
    } else {
      console.log('ℹ️ Manual payment gateway already exists');
    }

    console.log('🎉 Payment gateways setup completed successfully!');
    
    // Display current gateways
    const allGateways = await db.query.paymentGateways.findMany();
    console.log('\n📋 Current payment gateways:');
    allGateways.forEach(gateway => {
      console.log(`  - ${gateway.displayName} (${gateway.type}) - ${gateway.isActive ? 'Active' : 'Inactive'}`);
    });

  } catch (error) {
    console.error('❌ Error setting up payment gateways:', error);
    throw error;
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupPaymentGateways()
    .then(() => {
      console.log('✅ Setup completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Setup failed:', error);
      process.exit(1);
    });
}

export { setupPaymentGateways };
