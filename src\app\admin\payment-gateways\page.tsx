"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import {
  CreditCardIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  Cog6ToothIcon,
  BanknotesIcon,
  CheckCircleIcon,
  XCircleIcon
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { PaymentGatewayConfigModal } from "@/components/admin/PaymentGatewayConfigModal";

interface PaymentGateway {
  id: string;
  name: string;
  displayName: string;
  type: string;
  isActive: boolean;
  config: any;
  depositFee: string;
  depositFixedFee: string;
  minDeposit: string;
  maxDeposit: string;
  currency: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export default function PaymentGatewaysPage() {
  const [gateways, setGateways] = useState<PaymentGateway[]>([]);
  const [loading, setLoading] = useState(true);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    fetchGateways();
  }, []);

  const fetchGateways = async () => {
    try {
      const response = await fetch('/api/admin/payment-gateways');
      const data = await response.json();

      if (data.success) {
        setGateways(data.data);
      } else {
        toast.error('Failed to fetch payment gateways');
      }
    } catch (error) {
      console.error('Error fetching gateways:', error);
      toast.error('Failed to fetch payment gateways');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGateway = () => {
    setSelectedGateway(null);
    setModalMode('create');
    setIsConfigModalOpen(true);
  };

  const handleEditGateway = (gateway: PaymentGateway) => {
    setSelectedGateway(gateway);
    setModalMode('edit');
    setIsConfigModalOpen(true);
  };

  const handleToggleStatus = async (gatewayId: string) => {
    try {
      const response = await fetch(`/api/admin/payment-gateways/${gatewayId}/toggle`, {
        method: 'POST',
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        fetchGateways();
      } else {
        toast.error(data.message || 'Failed to update gateway status');
      }
    } catch (error) {
      console.error('Error toggling gateway status:', error);
      toast.error('Failed to update gateway status');
    }
  };

  const handleDeleteGateway = async (gatewayId: string, gatewayName: string) => {
    if (!confirm(`Are you sure you want to delete the "${gatewayName}" payment gateway? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/payment-gateways/${gatewayId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        fetchGateways();
      } else {
        toast.error(data.message || 'Failed to delete gateway');
      }
    } catch (error) {
      console.error('Error deleting gateway:', error);
      toast.error('Failed to delete gateway');
    }
  };

  const getGatewayIcon = (type: string) => {
    switch (type) {
      case 'stripe':
        return <CreditCardIcon className="h-5 w-5" />;
      case 'paypal':
        return <BanknotesIcon className="h-5 w-5" />;
      case 'wallet':
        return <BanknotesIcon className="h-5 w-5" />;
      case 'manual':
        return <Cog6ToothIcon className="h-5 w-5" />;
      default:
        return <CreditCardIcon className="h-5 w-5" />;
    }
  };

  const getGatewayTypeLabel = (type: string) => {
    switch (type) {
      case 'stripe':
        return 'Stripe';
      case 'paypal':
        return 'PayPal';
      case 'wallet':
        return 'Wallet Balance';
      case 'manual':
        return 'Manual Payment';
      case 'uddoktapay':
        return 'UddoktaPay';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payment Gateways</h1>
            <p className="text-gray-600 mt-1">
              Manage payment gateways for your application
            </p>
          </div>
          <Button
            onClick={handleCreateGateway}
            className="flex items-center space-x-2"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Gateway</span>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CreditCardIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Gateways</p>
                <p className="text-2xl font-bold text-gray-900">{gateways.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircleIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Gateways</p>
                <p className="text-2xl font-bold text-gray-900">
                  {gateways.filter(g => g.isActive).length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircleIcon className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Inactive Gateways</p>
                <p className="text-2xl font-bold text-gray-900">
                  {gateways.filter(g => !g.isActive).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Gateways Table */}
        <div className="bg-white shadow-sm rounded-lg border overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Payment Gateways</h2>
          </div>
          
          {gateways.length === 0 ? (
            <div className="text-center py-12">
              <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No payment gateways</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating a new payment gateway.
              </p>
              <div className="mt-6">
                <Button onClick={handleCreateGateway}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Gateway
                </Button>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Gateway
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fees
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Limits
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {gateways.map((gateway) => (
                    <tr key={gateway.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 flex items-center justify-center bg-gray-100 rounded-lg">
                            {getGatewayIcon(gateway.type)}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {gateway.displayName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {gateway.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getGatewayTypeLabel(gateway.type)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          gateway.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {gateway.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div>{gateway.depositFee}% + ${gateway.depositFixedFee}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div>${gateway.minDeposit} - ${gateway.maxDeposit}</div>
                          <div className="text-xs text-gray-500">{gateway.currency}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleToggleStatus(gateway.id)}
                            className="flex items-center space-x-1"
                          >
                            {gateway.isActive ? (
                              <EyeSlashIcon className="h-4 w-4" />
                            ) : (
                              <EyeIcon className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditGateway(gateway)}
                            className="flex items-center space-x-1"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteGateway(gateway.id, gateway.displayName)}
                            className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Gateway Configuration Modal */}
      <PaymentGatewayConfigModal
        isOpen={isConfigModalOpen}
        onClose={() => setIsConfigModalOpen(false)}
        onSuccess={() => {
          setIsConfigModalOpen(false);
          fetchGateways();
        }}
        gateway={selectedGateway}
        mode={modalMode}
      />
    </AdminLayout>
  );
}
