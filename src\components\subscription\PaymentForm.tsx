"use client";

import { useState, useEffect } from "react";
import { CreditCardIcon, WalletIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { toast } from "react-hot-toast";

interface PaymentGateway {
  id: string;
  name: string;
  displayName: string;
  type: string;
  isActive?: boolean;
  depositFee?: string;
  depositFixedFee?: string;
  minDeposit?: string;
  maxDeposit?: string;
  currency?: string;
}

interface WalletBalance {
  generalBalance: string;
  earningBalance: string;
  totalDeposited: string;
  totalWithdrawn: string;
  totalSent: string;
  totalReceived: string;
}

interface PaymentFormProps {
  subscriptionId: string;
  planName: string;
  amount: string;
  currency: string;
  onPaymentSuccess: (data: any) => void;
  onPaymentError: (error: string) => void;
  onCancel: () => void;
}

export function PaymentForm({
  subscriptionId,
  planName,
  amount,
  currency,
  onPaymentSuccess,
  onPaymentError,
  onCancel
}: PaymentFormProps) {
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([]);
  const [selectedGateway, setSelectedGateway] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [gatewaysLoading, setGatewaysLoading] = useState(true);
  const [walletBalance, setWalletBalance] = useState<WalletBalance | null>(null);
  const [walletLoading, setWalletLoading] = useState(true);

  useEffect(() => {
    fetchPaymentGateways();
    fetchWalletBalance();
  }, []);

  const fetchPaymentGateways = async () => {
    try {
      // Try the primary endpoint first
      let response = await fetch('/api/payment-gateways');
      let result = await response.json();

      // If primary endpoint fails, try the wallet gateways endpoint
      if (!result.success) {
        response = await fetch('/api/wallet/gateways');
        result = await response.json();
      }

      if (result.success) {
        console.log('Payment gateways loaded:', result.data);
        console.log('Wallet gateway found:', result.data.find(g => g.type === 'wallet'));
        setPaymentGateways(result.data);

        // Auto-select first gateway if available
        if (result.data.length > 0) {
          setSelectedGateway(result.data[0].id);
        }
      } else {
        console.error('Failed to fetch payment gateways:', result);
        toast.error('Failed to fetch payment methods');
      }
    } catch (error) {
      console.error('Error fetching payment gateways:', error);
      // Try fallback endpoint
      try {
        const fallbackResponse = await fetch('/api/wallet/gateways');
        const fallbackResult = await fallbackResponse.json();

        if (fallbackResult.success) {
          setPaymentGateways(fallbackResult.data);
          if (fallbackResult.data.length > 0) {
            setSelectedGateway(fallbackResult.data[0].id);
          }
        } else {
          toast.error('Failed to fetch payment methods');
        }
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
        toast.error('Failed to fetch payment methods');
      }
    } finally {
      setGatewaysLoading(false);
    }
  };

  const fetchWalletBalance = async () => {
    try {
      const response = await fetch('/api/wallet/balance');
      const result = await response.json();

      if (result.success) {
        console.log('Wallet balance loaded:', result.data);
        setWalletBalance(result.data);
      } else {
        console.error('Failed to load wallet balance:', result.message);
        // Don't show error toast for wallet balance fetch failure
        // as it's not critical for non-wallet payments
      }
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      // Set wallet balance to null so wallet payment option is disabled
      setWalletBalance(null);
    } finally {
      setWalletLoading(false);
    }
  };

  const handlePayment = async () => {
    if (!selectedGateway) {
      toast.error('Please select a payment method');
      return;
    }

    // Validate selected gateway exists and is active
    const gateway = paymentGateways.find(g => g.id === selectedGateway);
    console.log('Selected gateway:', selectedGateway);
    console.log('Found gateway:', gateway);
    console.log('Available gateways:', paymentGateways);

    if (!gateway) {
      toast.error('Selected payment method not found');
      return;
    }

    // Check if gateway is active (some gateways might not have isActive property)
    if (gateway.hasOwnProperty('isActive') && !gateway.isActive) {
      toast.error('Selected payment method is not active');
      return;
    }

    // Validate amount
    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      toast.error('Invalid payment amount');
      return;
    }

    // Validate wallet balance for wallet payments
    if (gateway.type === 'wallet') {
      if (!walletBalance) {
        toast.error('Unable to load wallet balance. Please try again.');
        return;
      }

      const currentBalance = parseFloat(walletBalance.generalBalance);
      if (currentBalance < numericAmount) {
        toast.error(`Insufficient wallet balance. Available: $${currentBalance.toFixed(2)}, Required: $${numericAmount.toFixed(2)}`);
        return;
      }
    }

    setLoading(true);

    try {
      const response = await fetch('/api/subscription-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId,
          paymentGatewayId: selectedGateway,
          returnUrl: `${window.location.origin}/manage-subscription?payment=success`,
          cancelUrl: `${window.location.origin}/manage-subscription?payment=cancelled`,
        }),
      });

      const result = await response.json();

      if (result.success) {
        if (result.data.paymentUrl) {
          // Redirect to payment gateway
          window.location.href = result.data.paymentUrl;
        } else {
          // Payment completed immediately (e.g., wallet payment)
          if (result.data.walletPayment) {
            toast.success('Payment completed successfully using wallet balance!');
            // Refresh wallet balance after successful payment
            fetchWalletBalance();
          }
          onPaymentSuccess(result.data);
        }
      } else {
        // Enhanced error handling for different error types
        if (result.message?.includes('Insufficient wallet balance')) {
          toast.error(result.message);
          // Refresh wallet balance to show current amount
          fetchWalletBalance();
        } else if (result.message?.includes('Wallet not found')) {
          toast.error('Wallet error. Please try again or contact support.');
        } else if (result.message?.includes('Subscription not found')) {
          toast.error('Subscription error. Please try again.');
        } else {
          onPaymentError(result.message || 'Payment processing failed');
        }
      }
    } catch (error) {
      console.error('Error processing payment:', error);

      // More specific error handling
      if (error instanceof TypeError && error.message.includes('fetch')) {
        onPaymentError('Network error. Please check your connection and try again.');
      } else if (error instanceof Error) {
        onPaymentError(error.message || 'Payment processing failed');
      } else {
        onPaymentError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const getGatewayIcon = (type: string) => {
    switch (type) {

      case 'uddoktapay':
        return (
          <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded">
            <span className="text-green-600 font-bold text-xs">UP</span>
          </div>
        );
      case 'manual':
        return (
          <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded">
            <CreditCardIcon className="h-4 w-4 text-gray-600" />
          </div>
        );
      case 'wallet':
        return (
          <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded">
            <WalletIcon className="h-4 w-4 text-green-600" />
          </div>
        );
      default:
        return (
          <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded">
            <CreditCardIcon className="h-4 w-4 text-gray-600" />
          </div>
        );
    }
  };

  const formatAmount = (amount: string, currency: string) => {
    const value = parseFloat(amount);
    const symbol = currency === 'USD' ? '$' : currency;
    return `${symbol}${value.toFixed(2)}`;
  };

  if (gatewaysLoading) {
    return (
      <div className="space-y-6">
        {/* Order Summary Skeleton */}
        <div className="bg-gray-50 rounded-lg p-4 animate-pulse">
          <div className="h-5 bg-gray-200 rounded w-1/3 mb-3"></div>
          <div className="flex justify-between items-center mb-2">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          </div>
          <div className="mt-2 pt-2 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>

        {/* Payment Methods Skeleton */}
        <div>
          <div className="h-5 bg-gray-200 rounded w-1/3 mb-4 animate-pulse"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border rounded-lg p-4 animate-pulse">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-200 rounded mr-3"></div>
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-24 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Button Skeleton */}
        <div className="flex space-x-3">
          <div className="flex-1 h-10 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-20 h-10 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  if (paymentGateways.length === 0) {
    return (
      <div className="text-center py-8">
        <CreditCardIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No payment methods available</h3>
        <p className="text-gray-500 mb-4">
          Please contact support to set up payment methods.
        </p>
        <Button onClick={onCancel} variant="outline">
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Order Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Order Summary</h3>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">{planName} Subscription</span>
          <span className="text-lg font-semibold text-gray-900">
            {formatAmount(amount, currency)}
          </span>
        </div>
        <div className="mt-2 pt-2 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <span className="font-medium text-gray-900">Total</span>
            <span className="text-xl font-bold text-gray-900">
              {formatAmount(amount, currency)}
            </span>
          </div>
        </div>
      </div>

      {/* Wallet Balance Display */}
      {!walletLoading && walletBalance && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <WalletIcon className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-sm font-medium text-green-800">General Wallet Balance</span>
            </div>
            <span className="text-lg font-bold text-green-700">
              ${parseFloat(walletBalance.generalBalance).toFixed(2)}
            </span>
          </div>
          {parseFloat(walletBalance.generalBalance) < parseFloat(amount) && (
            <div className="mt-2 text-xs text-orange-600 bg-orange-50 border border-orange-200 rounded px-2 py-1">
              ⚠️ Insufficient balance for this subscription. Required: ${parseFloat(amount).toFixed(2)}
            </div>
          )}
        </div>
      )}

      {/* Payment Method Selection */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Select Payment Method</h3>
        <div className="grid gap-3">
          {paymentGateways.map((gateway) => (
            <label
              key={gateway.id}
              className={`relative flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-md ${
                selectedGateway === gateway.id
                  ? 'border-blue-500 bg-blue-50 shadow-sm'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name="paymentGateway"
                value={gateway.id}
                checked={selectedGateway === gateway.id}
                onChange={(e) => setSelectedGateway(e.target.value)}
                className="sr-only"
              />
              <div className="flex items-center flex-1">
                <div className="mr-4">
                  {getGatewayIcon(gateway.type)}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-semibold text-gray-900">
                    {gateway.displayName}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {gateway.type === 'uddoktapay' && 'Mobile Banking, Cards, Net Banking'}
                    {gateway.type === 'manual' && 'Manual payment processing'}
                    {gateway.type === 'wallet' && 'Pay using your general wallet balance'}
                    {!['uddoktapay', 'manual', 'wallet'].includes(gateway.type) &&
                      `Pay securely with ${gateway.displayName}`}
                  </p>
                  {gateway.depositFee && parseFloat(gateway.depositFee) > 0 && (
                    <p className="text-xs text-orange-600 mt-1">
                      Fee: {gateway.depositFee}% {gateway.depositFixedFee && parseFloat(gateway.depositFixedFee) > 0 &&
                        `+ ${formatAmount(gateway.depositFixedFee, gateway.currency || currency)}`}
                    </p>
                  )}
                </div>
              </div>
              {selectedGateway === gateway.id && (
                <div className="ml-3">
                  <div className="flex items-center justify-center w-5 h-5 bg-blue-600 rounded-full">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              )}
            </label>
          ))}
        </div>
      </div>

      {/* Payment Actions */}
      <div className="space-y-4">
        <Button
          onClick={handlePayment}
          disabled={loading || !selectedGateway}
          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
              <span>Processing Payment...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center">
              <CreditCardIcon className="h-5 w-5 mr-3" />
              <span>Pay {formatAmount(amount, currency)} Now</span>
            </div>
          )}
        </Button>

        <Button
          onClick={onCancel}
          variant="outline"
          disabled={loading}
          className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 py-3 px-6 rounded-xl transition-all duration-200"
        >
          Cancel Payment
        </Button>
      </div>

      {/* Security Notice */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center justify-center space-x-2 text-green-700">
          <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
          <span className="text-sm font-medium">Secure Payment</span>
        </div>
        <p className="text-xs text-green-600 text-center mt-2">
          Your payment information is protected with bank-level encryption and security
        </p>
      </div>
    </div>
  );
}
