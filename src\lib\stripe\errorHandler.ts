import { toast } from 'react-hot-toast';

export interface StripeErrorDetails {
  code: string;
  message: string;
  userMessage: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  shouldRetry: boolean;
  retryDelay?: number; // in milliseconds
}

// Comprehensive Stripe error mapping
export const STRIPE_ERROR_CODES: Record<string, StripeErrorDetails> = {
  // Card errors
  'card_declined': {
    code: 'card_declined',
    message: 'Your card was declined.',
    userMessage: 'Your card was declined. Please try a different payment method or contact your bank.',
    severity: 'medium',
    shouldRetry: false,
  },
  'expired_card': {
    code: 'expired_card',
    message: 'Your card has expired.',
    userMessage: 'Your card has expired. Please update your payment information with a valid card.',
    severity: 'medium',
    shouldRetry: false,
  },
  'insufficient_funds': {
    code: 'insufficient_funds',
    message: 'Your card has insufficient funds.',
    userMessage: 'Your card has insufficient funds. Please try a different payment method.',
    severity: 'medium',
    shouldRetry: false,
  },
  'incorrect_cvc': {
    code: 'incorrect_cvc',
    message: 'Your card\'s security code is incorrect.',
    userMessage: 'The security code (CVC) you entered is incorrect. Please check and try again.',
    severity: 'low',
    shouldRetry: true,
  },
  'incorrect_number': {
    code: 'incorrect_number',
    message: 'Your card number is incorrect.',
    userMessage: 'The card number you entered is incorrect. Please check and try again.',
    severity: 'low',
    shouldRetry: true,
  },
  'invalid_expiry_month': {
    code: 'invalid_expiry_month',
    message: 'Your card\'s expiration month is invalid.',
    userMessage: 'The expiration month you entered is invalid. Please check and try again.',
    severity: 'low',
    shouldRetry: true,
  },
  'invalid_expiry_year': {
    code: 'invalid_expiry_year',
    message: 'Your card\'s expiration year is invalid.',
    userMessage: 'The expiration year you entered is invalid. Please check and try again.',
    severity: 'low',
    shouldRetry: true,
  },
  'processing_error': {
    code: 'processing_error',
    message: 'An error occurred while processing your card.',
    userMessage: 'There was an error processing your payment. Please try again in a few moments.',
    severity: 'medium',
    shouldRetry: true,
    retryDelay: 5000,
  },

  // Rate limiting
  'rate_limit': {
    code: 'rate_limit',
    message: 'Too many requests made to the API too quickly.',
    userMessage: 'Too many payment attempts. Please wait a moment and try again.',
    severity: 'medium',
    shouldRetry: true,
    retryDelay: 10000,
  },

  // API errors
  'api_error': {
    code: 'api_error',
    message: 'An error occurred with Stripe\'s API.',
    userMessage: 'A payment processing error occurred. Please try again or contact support.',
    severity: 'high',
    shouldRetry: true,
    retryDelay: 5000,
  },
  'connection_error': {
    code: 'connection_error',
    message: 'A network error occurred.',
    userMessage: 'Connection error. Please check your internet connection and try again.',
    severity: 'medium',
    shouldRetry: true,
    retryDelay: 3000,
  },
  'authentication_error': {
    code: 'authentication_error',
    message: 'Authentication with Stripe\'s API failed.',
    userMessage: 'Payment system configuration error. Please contact support.',
    severity: 'critical',
    shouldRetry: false,
  },

  // Invalid request errors
  'invalid_request_error': {
    code: 'invalid_request_error',
    message: 'Invalid parameters were supplied to Stripe\'s API.',
    userMessage: 'Invalid payment information. Please check your details and try again.',
    severity: 'medium',
    shouldRetry: true,
  },

  // Webhook errors
  'webhook_signature_verification_failed': {
    code: 'webhook_signature_verification_failed',
    message: 'Webhook signature verification failed.',
    userMessage: 'Payment verification failed. Please contact support if you were charged.',
    severity: 'high',
    shouldRetry: false,
  },

  // Subscription errors
  'subscription_creation_failed': {
    code: 'subscription_creation_failed',
    message: 'Failed to create subscription.',
    userMessage: 'Unable to create subscription. Please try again or contact support.',
    severity: 'high',
    shouldRetry: true,
    retryDelay: 5000,
  },
  'customer_creation_failed': {
    code: 'customer_creation_failed',
    message: 'Failed to create customer.',
    userMessage: 'Unable to set up payment profile. Please try again.',
    severity: 'medium',
    shouldRetry: true,
    retryDelay: 3000,
  },

  // Generic fallback
  'unknown_error': {
    code: 'unknown_error',
    message: 'An unexpected error occurred.',
    userMessage: 'An unexpected error occurred. Please try again or contact support.',
    severity: 'medium',
    shouldRetry: true,
    retryDelay: 5000,
  },
};

export class StripeErrorHandler {
  /**
   * Get error details for a given error code
   */
  static getErrorDetails(errorCode: string): StripeErrorDetails {
    return STRIPE_ERROR_CODES[errorCode] || STRIPE_ERROR_CODES['unknown_error'];
  }

  /**
   * Handle Stripe errors with appropriate user feedback
   */
  static handleError(error: any, context?: string): StripeErrorDetails {
    let errorCode = 'unknown_error';
    let errorMessage = 'An unexpected error occurred.';

    // Extract error code from different error types
    if (error?.code) {
      errorCode = error.code;
    } else if (error?.type) {
      errorCode = error.type;
    } else if (error?.decline_code) {
      errorCode = error.decline_code;
    }

    // Extract error message
    if (error?.message) {
      errorMessage = error.message;
    }

    const errorDetails = this.getErrorDetails(errorCode);

    // Log error for debugging
    this.logError(error, errorDetails, context);

    // Show user-friendly toast message
    this.showUserFeedback(errorDetails);

    return errorDetails;
  }

  /**
   * Log error details for debugging and monitoring
   */
  private static logError(
    originalError: any,
    errorDetails: StripeErrorDetails,
    context?: string
  ): void {
    const logData = {
      timestamp: new Date().toISOString(),
      context: context || 'stripe_operation',
      errorCode: errorDetails.code,
      severity: errorDetails.severity,
      originalError: {
        message: originalError?.message,
        code: originalError?.code,
        type: originalError?.type,
        decline_code: originalError?.decline_code,
        stack: originalError?.stack,
      },
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    };

    // Log to console (in production, you'd send this to your logging service)
    if (errorDetails.severity === 'critical' || errorDetails.severity === 'high') {
      console.error('Stripe Error:', logData);
    } else {
      console.warn('Stripe Warning:', logData);
    }

    // In production, send to monitoring service
    // Example: sendToMonitoringService(logData);
  }

  /**
   * Show appropriate user feedback based on error severity
   */
  private static showUserFeedback(errorDetails: StripeErrorDetails): void {
    switch (errorDetails.severity) {
      case 'critical':
        toast.error(errorDetails.userMessage, {
          duration: 8000,
          icon: '🚨',
        });
        break;
      case 'high':
        toast.error(errorDetails.userMessage, {
          duration: 6000,
        });
        break;
      case 'medium':
        toast.error(errorDetails.userMessage, {
          duration: 4000,
        });
        break;
      case 'low':
        toast.error(errorDetails.userMessage, {
          duration: 3000,
        });
        break;
    }
  }

  /**
   * Determine if an operation should be retried
   */
  static shouldRetry(errorDetails: StripeErrorDetails, attemptCount: number = 0): boolean {
    const maxRetries = 3;
    return errorDetails.shouldRetry && attemptCount < maxRetries;
  }

  /**
   * Get retry delay for an error
   */
  static getRetryDelay(errorDetails: StripeErrorDetails, attemptCount: number = 0): number {
    const baseDelay = errorDetails.retryDelay || 5000;
    // Exponential backoff
    return baseDelay * Math.pow(2, attemptCount);
  }

  /**
   * Retry an operation with exponential backoff
   */
  static async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    context?: string
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        const errorDetails = this.handleError(error, context);
        
        if (attempt === maxRetries || !this.shouldRetry(errorDetails, attempt)) {
          throw error;
        }
        
        const delay = this.getRetryDelay(errorDetails, attempt);
        console.log(`Retrying operation in ${delay}ms (attempt ${attempt + 1}/${maxRetries})`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }
}

// Utility function for handling async Stripe operations with error handling
export async function withStripeErrorHandling<T>(
  operation: () => Promise<T>,
  context?: string
): Promise<{ success: boolean; data?: T; error?: StripeErrorDetails }> {
  try {
    const data = await operation();
    return { success: true, data };
  } catch (error) {
    const errorDetails = StripeErrorHandler.handleError(error, context);
    return { success: false, error: errorDetails };
  }
}
