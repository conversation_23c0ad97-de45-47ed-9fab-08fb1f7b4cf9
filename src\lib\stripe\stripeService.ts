import Stripe from 'stripe';
import { createStripeInstance, handleStripeError, StripeConfig } from './config';
import { StripeErrorHandler, withStripeErrorHandling } from './errorHandler';

export interface StripePaymentRequest {
  amount: number; // Amount in cents
  currency: string;
  userId: string;
  metadata: {
    transactionId: string;
    type: 'deposit' | 'subscription_payment' | 'subscription_payment_retry';
    returnUrl?: string;
    cancelUrl?: string;
    [key: string]: any;
  };
  config: StripeConfig;
}

export interface StripePaymentResponse {
  success: boolean;
  paymentIntentId?: string;
  clientSecret?: string;
  paymentUrl?: string;
  error?: string;
  code?: string;
}

export interface StripeSubscriptionRequest {
  customerId?: string;
  customerEmail: string;
  priceId: string;
  userId: string;
  metadata: {
    subscriptionId: string;
    planId: string;
    userId: string;
    [key: string]: any;
  };
  config: StripeConfig;
}

export interface StripeSubscriptionResponse {
  success: boolean;
  subscriptionId?: string;
  clientSecret?: string;
  customerId?: string;
  error?: string;
  code?: string;
}

export class StripeService {
  /**
   * Create a payment intent for one-time payments (deposits)
   */
  static async createPaymentIntent(request: StripePaymentRequest): Promise<StripePaymentResponse> {
    const result = await withStripeErrorHandling(async () => {
      const stripe = createStripeInstance(request.config);

      // Convert amount to cents if it's not already
      const amountInCents = Math.round(request.amount * 100);

      const paymentIntent = await stripe.paymentIntents.create({
        amount: amountInCents,
        currency: request.currency.toLowerCase(),
        metadata: {
          userId: request.userId,
          transactionId: request.metadata.transactionId,
          type: request.metadata.type,
          ...request.metadata,
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return {
        paymentIntentId: paymentIntent.id,
        clientSecret: paymentIntent.client_secret!,
      };
    }, 'payment_intent_creation');

    if (result.success && result.data) {
      return {
        success: true,
        paymentIntentId: result.data.paymentIntentId,
        clientSecret: result.data.clientSecret,
      };
    } else {
      return {
        success: false,
        error: result.error?.userMessage || 'Payment intent creation failed',
        code: result.error?.code,
      };
    }
  }

  /**
   * Create or retrieve a Stripe customer
   */
  static async createOrGetCustomer(
    stripe: Stripe,
    email: string,
    userId: string,
    name?: string
  ): Promise<Stripe.Customer> {
    // First, try to find existing customer by email
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      return existingCustomers.data[0];
    }

    // Create new customer
    return await stripe.customers.create({
      email: email,
      name: name,
      metadata: {
        userId: userId,
      },
    });
  }

  /**
   * Create a subscription for recurring payments
   */
  static async createSubscription(request: StripeSubscriptionRequest): Promise<StripeSubscriptionResponse> {
    try {
      const stripe = createStripeInstance(request.config);

      // Create or get customer
      let customer: Stripe.Customer;
      if (request.customerId) {
        customer = await stripe.customers.retrieve(request.customerId) as Stripe.Customer;
      } else {
        customer = await this.createOrGetCustomer(
          stripe,
          request.customerEmail,
          request.userId
        );
      }

      // Create subscription
      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [
          {
            price: request.priceId,
          },
        ],
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription',
        },
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          userId: request.userId,
          subscriptionId: request.metadata.subscriptionId,
          planId: request.metadata.planId,
          ...request.metadata,
        },
      });

      const invoice = subscription.latest_invoice as Stripe.Invoice;
      const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;

      return {
        success: true,
        subscriptionId: subscription.id,
        clientSecret: paymentIntent.client_secret!,
        customerId: customer.id,
      };
    } catch (error: any) {
      console.error('Stripe subscription creation error:', error);
      const stripeError = handleStripeError(error);
      return {
        success: false,
        error: stripeError.message,
        code: stripeError.code,
      };
    }
  }

  /**
   * Cancel a subscription
   */
  static async cancelSubscription(
    subscriptionId: string,
    config: StripeConfig,
    cancelAtPeriodEnd: boolean = true
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const stripe = createStripeInstance(config);

      if (cancelAtPeriodEnd) {
        await stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true,
        });
      } else {
        await stripe.subscriptions.cancel(subscriptionId);
      }

      return { success: true };
    } catch (error: any) {
      console.error('Stripe subscription cancellation error:', error);
      const stripeError = handleStripeError(error);
      return {
        success: false,
        error: stripeError.message,
      };
    }
  }

  /**
   * Retrieve payment intent status
   */
  static async getPaymentIntent(
    paymentIntentId: string,
    config: StripeConfig
  ): Promise<Stripe.PaymentIntent | null> {
    try {
      const stripe = createStripeInstance(config);
      return await stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (error: any) {
      console.error('Error retrieving payment intent:', error);
      return null;
    }
  }

  /**
   * Retrieve subscription status
   */
  static async getSubscription(
    subscriptionId: string,
    config: StripeConfig
  ): Promise<Stripe.Subscription | null> {
    try {
      const stripe = createStripeInstance(config);
      return await stripe.subscriptions.retrieve(subscriptionId);
    } catch (error: any) {
      console.error('Error retrieving subscription:', error);
      return null;
    }
  }

  /**
   * Create a price for a subscription plan
   */
  static async createPrice(
    amount: number,
    currency: string,
    interval: 'month' | 'year',
    productId: string,
    config: StripeConfig
  ): Promise<{ success: boolean; priceId?: string; error?: string }> {
    try {
      const stripe = createStripeInstance(config);
      const amountInCents = Math.round(amount * 100);

      const price = await stripe.prices.create({
        unit_amount: amountInCents,
        currency: currency.toLowerCase(),
        recurring: {
          interval: interval,
        },
        product: productId,
      });

      return {
        success: true,
        priceId: price.id,
      };
    } catch (error: any) {
      console.error('Stripe price creation error:', error);
      const stripeError = handleStripeError(error);
      return {
        success: false,
        error: stripeError.message,
      };
    }
  }

  /**
   * Create a product for subscription plans
   */
  static async createProduct(
    name: string,
    description: string,
    config: StripeConfig
  ): Promise<{ success: boolean; productId?: string; error?: string }> {
    try {
      const stripe = createStripeInstance(config);

      const product = await stripe.products.create({
        name: name,
        description: description,
      });

      return {
        success: true,
        productId: product.id,
      };
    } catch (error: any) {
      console.error('Stripe product creation error:', error);
      const stripeError = handleStripeError(error);
      return {
        success: false,
        error: stripeError.message,
      };
    }
  }
}
